-- Migration: Cleanup Voice Schema and Enhance API Keys
-- Description: Remove voice_sessions and voice_messages tables, enhance API keys for external users
-- Author: AI Assistant
-- Date: 2025-06-16

-- ============================================================================
-- STEP 1: Remove Voice Sessions and Voice Messages Tables
-- ============================================================================

-- Drop dependent objects first (indexes, policies, etc.)
DROP INDEX IF EXISTS idx_voice_sessions_user_active;
DROP INDEX IF EXISTS idx_voice_messages_user_session;
DROP INDEX IF EXISTS idx_voice_messages_unsent;
DROP INDEX IF EXISTS idx_voice_messages_unspoken;
DROP INDEX IF EXISTS idx_voice_messages_recent;
DROP INDEX IF EXISTS idx_voice_messages_user_only;

-- Drop RLS policies
DROP POLICY IF EXISTS "Users can view their own voice sessions" ON voice_sessions;
DROP POLICY IF EXISTS "Users can insert their own voice sessions" ON voice_sessions;
DROP POLICY IF EXISTS "Users can update their own voice sessions" ON voice_sessions;
DROP POLICY IF EXISTS "Users can view their own voice messages" ON voice_messages;
DROP POLICY IF EXISTS "Users can insert their own voice messages" ON voice_messages;
DROP POLICY IF EXISTS "Users can update their own voice messages" ON voice_messages;

-- Drop the tables (voice_messages first due to foreign key)
DROP TABLE IF EXISTS voice_messages CASCADE;
DROP TABLE IF EXISTS voice_sessions CASCADE;

-- ============================================================================
-- STEP 2: Enhance API Keys Table for External Users
-- ============================================================================

-- Add columns for external user management
ALTER TABLE api_keys ADD COLUMN IF NOT EXISTS name VARCHAR(255);
ALTER TABLE api_keys ADD COLUMN IF NOT EXISTS description TEXT;
ALTER TABLE api_keys ADD COLUMN IF NOT EXISTS last_used_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE api_keys ADD COLUMN IF NOT EXISTS usage_count INTEGER DEFAULT 0;
ALTER TABLE api_keys ADD COLUMN IF NOT EXISTS rate_limit_per_hour INTEGER DEFAULT 1000;
ALTER TABLE api_keys ADD COLUMN IF NOT EXISTS allowed_origins TEXT[];
ALTER TABLE api_keys ADD COLUMN IF NOT EXISTS permissions JSONB DEFAULT '{"voice": true, "messages": true}'::jsonb;

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_api_keys_user_active ON api_keys(user_id, active) WHERE active = true;
CREATE INDEX IF NOT EXISTS idx_api_keys_last_used ON api_keys(last_used_at DESC) WHERE active = true;
CREATE INDEX IF NOT EXISTS idx_api_keys_usage ON api_keys(usage_count DESC) WHERE active = true;

-- Update RLS policies for enhanced API keys
DROP POLICY IF EXISTS "Users can view their own API keys" ON api_keys;
DROP POLICY IF EXISTS "Users can insert their own API keys" ON api_keys;
DROP POLICY IF EXISTS "Users can update their own API keys" ON api_keys;

-- Create enhanced RLS policies
CREATE POLICY "Users can view their own API keys" ON api_keys
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own API keys" ON api_keys
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own API keys" ON api_keys
  FOR UPDATE USING (auth.uid() = user_id);

-- ============================================================================
-- STEP 3: Create External Users Table (Optional)
-- ============================================================================

-- Table to track external users who authenticate via API keys
CREATE TABLE IF NOT EXISTS external_users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  api_key_id UUID REFERENCES api_keys(id) ON DELETE CASCADE,
  external_user_id VARCHAR(255) NOT NULL, -- ID from external system
  display_name VARCHAR(255),
  email VARCHAR(255),
  metadata JSONB DEFAULT '{}'::jsonb,
  first_seen_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_seen_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for external users
CREATE INDEX IF NOT EXISTS idx_external_users_api_key ON external_users(api_key_id);
CREATE INDEX IF NOT EXISTS idx_external_users_external_id ON external_users(external_user_id);
CREATE INDEX IF NOT EXISTS idx_external_users_last_seen ON external_users(last_seen_at DESC);

-- RLS for external users
ALTER TABLE external_users ENABLE ROW LEVEL SECURITY;

CREATE POLICY "API key owners can view their external users" ON external_users
  FOR SELECT USING (
    api_key_id IN (
      SELECT id FROM api_keys WHERE user_id = auth.uid() AND active = true
    )
  );

CREATE POLICY "API key owners can insert their external users" ON external_users
  FOR INSERT WITH CHECK (
    api_key_id IN (
      SELECT id FROM api_keys WHERE user_id = auth.uid() AND active = true
    )
  );

CREATE POLICY "API key owners can update their external users" ON external_users
  FOR UPDATE USING (
    api_key_id IN (
      SELECT id FROM api_keys WHERE user_id = auth.uid() AND active = true
    )
  );

-- ============================================================================
-- STEP 4: Update Simple Messages for API Key Support
-- ============================================================================

-- Add API key tracking to simple messages
ALTER TABLE simple_messages ADD COLUMN IF NOT EXISTS api_key_id UUID REFERENCES api_keys(id);
ALTER TABLE simple_messages ADD COLUMN IF NOT EXISTS external_user_id VARCHAR(255);
ALTER TABLE simple_messages ADD COLUMN IF NOT EXISTS user_id UUID REFERENCES auth.users(id);

-- Add indexes for API key queries
CREATE INDEX IF NOT EXISTS idx_simple_messages_api_key ON simple_messages(api_key_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_simple_messages_external_user ON simple_messages(external_user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_simple_messages_user_id ON simple_messages(user_id, created_at DESC);

-- Enable RLS on simple_messages
ALTER TABLE simple_messages ENABLE ROW LEVEL SECURITY;

-- RLS policies for simple_messages
CREATE POLICY "Users can view their own messages" ON simple_messages
  FOR SELECT USING (
    auth.uid() = user_id OR 
    api_key_id IN (
      SELECT id FROM api_keys WHERE user_id = auth.uid() AND active = true
    ) OR
    user_id IS NULL -- Allow anonymous messages for backward compatibility
  );

CREATE POLICY "Users can insert their own messages" ON simple_messages
  FOR INSERT WITH CHECK (
    auth.uid() = user_id OR 
    api_key_id IN (
      SELECT id FROM api_keys WHERE user_id = auth.uid() AND active = true
    ) OR
    user_id IS NULL -- Allow anonymous messages for backward compatibility
  );

CREATE POLICY "Users can update their own messages" ON simple_messages
  FOR UPDATE USING (
    auth.uid() = user_id OR 
    api_key_id IN (
      SELECT id FROM api_keys WHERE user_id = auth.uid() AND active = true
    ) OR
    user_id IS NULL -- Allow anonymous messages for backward compatibility
  );

-- ============================================================================
-- STEP 5: Add Comments and Documentation
-- ============================================================================

-- Update comments
COMMENT ON TABLE api_keys IS 'API keys for external system authentication with enhanced features';
COMMENT ON COLUMN api_keys.name IS 'Human-readable name for the API key';
COMMENT ON COLUMN api_keys.description IS 'Description of what this API key is used for';
COMMENT ON COLUMN api_keys.last_used_at IS 'Timestamp of last API key usage';
COMMENT ON COLUMN api_keys.usage_count IS 'Total number of times this API key has been used';
COMMENT ON COLUMN api_keys.rate_limit_per_hour IS 'Maximum requests per hour for this API key';
COMMENT ON COLUMN api_keys.allowed_origins IS 'Array of allowed origins for CORS';
COMMENT ON COLUMN api_keys.permissions IS 'JSON object defining what this API key can access';

COMMENT ON TABLE external_users IS 'External users who authenticate via API keys';
COMMENT ON COLUMN external_users.external_user_id IS 'User ID from the external system';
COMMENT ON COLUMN external_users.display_name IS 'Display name for the external user';
COMMENT ON COLUMN external_users.metadata IS 'Additional metadata about the external user';

COMMENT ON COLUMN simple_messages.api_key_id IS 'API key used to create this message (for external users)';
COMMENT ON COLUMN simple_messages.external_user_id IS 'External user ID who created this message';
COMMENT ON COLUMN simple_messages.user_id IS 'Authenticated user ID (for logged-in users)';
