-- Migration: Add API Keys Table for MCP Authentication
-- Description: Creates table for long-lived API keys to replace JWT tokens
-- Author: <PERSON> (AI Architect)
-- Date: 2025-06-16

-- API keys table for MCP server authentication
CREATE TABLE api_keys (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  key_hash TEXT NOT NULL,
  key_prefix TEXT NOT NULL, -- For display and fast lookup (e.g., "aisdlc_a1b2c3d4")
  name TEXT NOT NULL DEFAULT 'MCP Server Key',
  last_used TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  active BOOLEAN DEFAULT true
);

-- Indexes for optimal performance
CREATE INDEX idx_api_keys_prefix ON api_keys(key_prefix) WHERE active = true;
CREATE INDEX idx_api_keys_user_active ON api_keys(user_id, active) WHERE active = true;
CREATE INDEX idx_api_keys_last_used ON api_keys(last_used DESC) WHERE active = true;

-- Row Level Security (RLS) policies
ALTER TABLE api_keys ENABLE ROW LEVEL SECURITY;

-- Users can only access their own API keys
CREATE POLICY "Users can view their own API keys" ON api_keys
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own API keys" ON api_keys
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own API keys" ON api_keys
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own API keys" ON api_keys
  FOR DELETE USING (auth.uid() = user_id);

-- Function to clean up old inactive API keys (optional, for maintenance)
CREATE OR REPLACE FUNCTION cleanup_inactive_api_keys()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM api_keys
  WHERE active = false
    AND created_at < NOW() - INTERVAL '90 days';

  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Comments for documentation
COMMENT ON TABLE api_keys IS 'Long-lived API keys for MCP server authentication';
COMMENT ON COLUMN api_keys.key_hash IS 'Bcrypt hash of the full API key';
COMMENT ON COLUMN api_keys.key_prefix IS 'Prefix for display and fast lookup (e.g., aisdlc_a1b2c3d4)';
COMMENT ON COLUMN api_keys.name IS 'User-friendly name for the API key';
COMMENT ON COLUMN api_keys.last_used IS 'Timestamp of last API request using this key';
COMMENT ON COLUMN api_keys.active IS 'Whether the API key is active and can be used';