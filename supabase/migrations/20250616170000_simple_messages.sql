-- Migration: Simple Messages Table
-- Description: Super simple message passing between voice app and MCP server
-- Author: <PERSON> (AI Architect)
-- Date: 2025-06-16

-- Simple messages table for basic message flow
CREATE TABLE simple_messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  text TEXT NOT NULL,
  role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'assistant')),
  ai_teammate VARCHAR(50),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index for chronological order
CREATE INDEX idx_simple_messages_created ON simple_messages(created_at DESC);

-- Index for role-based queries
CREATE INDEX idx_simple_messages_role ON simple_messages(role, created_at DESC);

-- Comments
COMMENT ON TABLE simple_messages IS 'Simple message passing between voice app and MCP server';
COMMENT ON COLUMN simple_messages.role IS 'Either user or assistant message';
COMMENT ON COLUMN simple_messages.ai_teammate IS 'Which AI teammate sent the message (alex, sarah, jordan, system)';
