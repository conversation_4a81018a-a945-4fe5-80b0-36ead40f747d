-- Migration: Add Voice Messaging Tables
-- Description: Adds tables for voice message storage and session management
-- Author: <PERSON> (AI Architect)
-- Date: 2025-06-16

-- Voice sessions table for conversation context
CREATE TABLE voice_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  active BOOLEAN DEFAULT true,
  last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Voice messages table for persistent message storage
CREATE TABLE voice_messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  session_id UUID REFERENCES voice_sessions(id) ON DELETE CASCADE,
  text TEXT NOT NULL,
  role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'assistant')),
  ai_<PERSON> VARCHA<PERSON>(50),
  spoken BOOLEAN DEFAULT false,
  sent BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for optimal query performance
CREATE INDEX idx_voice_sessions_user_active ON voice_sessions(user_id, active) WHERE active = true;
CREATE INDEX idx_voice_messages_user_session ON voice_messages(user_id, session_id);
CREATE INDEX idx_voice_messages_unsent ON voice_messages(user_id, sent, role) WHERE sent = false;
CREATE INDEX idx_voice_messages_unspoken ON voice_messages(user_id, spoken, role) WHERE spoken = false AND role = 'assistant';
CREATE INDEX idx_voice_messages_recent ON voice_messages(user_id, created_at DESC);

-- Row Level Security (RLS) policies
ALTER TABLE voice_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE voice_messages ENABLE ROW LEVEL SECURITY;

-- Users can only access their own voice sessions
CREATE POLICY "Users can view their own voice sessions" ON voice_sessions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own voice sessions" ON voice_sessions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own voice sessions" ON voice_sessions
  FOR UPDATE USING (auth.uid() = user_id);

-- Users can only access their own voice messages
CREATE POLICY "Users can view their own voice messages" ON voice_messages
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own voice messages" ON voice_messages
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own voice messages" ON voice_messages
  FOR UPDATE USING (auth.uid() = user_id);

-- Function to automatically update last_activity on voice_sessions
CREATE OR REPLACE FUNCTION update_voice_session_activity()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE voice_sessions
  SET last_activity = NOW()
  WHERE id = NEW.session_id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update session activity when new messages are added
CREATE TRIGGER trigger_update_voice_session_activity
  AFTER INSERT ON voice_messages
  FOR EACH ROW
  EXECUTE FUNCTION update_voice_session_activity();

-- Function to clean up old inactive sessions (optional, for maintenance)
CREATE OR REPLACE FUNCTION cleanup_inactive_voice_sessions()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM voice_sessions
  WHERE active = false
    AND last_activity < NOW() - INTERVAL '7 days';

  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Comments for documentation
COMMENT ON TABLE voice_sessions IS 'Stores voice conversation sessions for each user';
COMMENT ON TABLE voice_messages IS 'Stores individual voice messages within sessions';
COMMENT ON COLUMN voice_messages.role IS 'Either user or assistant message';
COMMENT ON COLUMN voice_messages.ai_teammate IS 'Identifies which AI teammate sent the message (sarah, jordan, alex, system)';
COMMENT ON COLUMN voice_messages.spoken IS 'Tracks if the message has been spoken via TTS';
COMMENT ON COLUMN voice_messages.sent IS 'Tracks if the message has been sent to the client';