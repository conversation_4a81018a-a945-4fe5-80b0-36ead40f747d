-- Migration: Simplify Voice Messages
-- Description: Remove session dependency and make voice_messages standalone
-- Author: <PERSON> (AI Architect)
-- Date: 2025-06-16

-- Drop the session dependency from voice_messages
ALTER TABLE voice_messages DROP CONSTRAINT voice_messages_session_id_fkey;
ALTER TABLE voice_messages ALTER COLUMN session_id DROP NOT NULL;

-- Make session_id optional (for backward compatibility)
ALTER TABLE voice_messages ALTER COLUMN session_id SET DEFAULT NULL;

-- Update RLS policies to work without sessions
DROP POLICY IF EXISTS "Users can view their own voice messages" ON voice_messages;
DROP POLICY IF EXISTS "Users can insert their own voice messages" ON voice_messages;
DROP POLICY IF EXISTS "Users can update their own voice messages" ON voice_messages;

-- Create simplified RLS policies that work with API keys
CREATE POLICY "Users can view their own voice messages" ON voice_messages
  FOR SELECT USING (
    auth.uid() = user_id OR 
    user_id IS NULL  -- Allow anonymous messages for backward compatibility
  );

CREATE POLICY "Users can insert their own voice messages" ON voice_messages
  FOR INSERT WITH CHECK (
    auth.uid() = user_id OR 
    user_id IS NULL  -- Allow anonymous messages for backward compatibility
  );

CREATE POLICY "Users can update their own voice messages" ON voice_messages
  FOR UPDATE USING (
    auth.uid() = user_id OR 
    user_id IS NULL  -- Allow anonymous messages for backward compatibility
  );

-- Add index for user-based queries without session
CREATE INDEX IF NOT EXISTS idx_voice_messages_user_only ON voice_messages(user_id, created_at DESC) WHERE user_id IS NOT NULL;

-- Comments
COMMENT ON COLUMN voice_messages.session_id IS 'Optional session ID - can be NULL for standalone messages';
COMMENT ON COLUMN voice_messages.user_id IS 'User ID from API key authentication - can be NULL for anonymous messages';
