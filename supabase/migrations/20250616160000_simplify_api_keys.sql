-- Migration: Simplify API Keys Table
-- Description: Remove all the complex stuff - just simple random strings
-- Author: <PERSON> (AI Architect) 
-- Date: 2025-06-16

-- Drop the overcomplicated table
DROP TABLE IF EXISTS api_keys CASCADE;

-- Create simple API keys table
CREATE TABLE api_keys (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  api_key TEXT NOT NULL UNIQUE,
  active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Simple index for fast lookup
CREATE INDEX idx_api_keys_lookup ON api_keys(api_key, active) WHERE active = true;

-- RLS policies
ALTER TABLE api_keys ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own API keys" ON api_keys
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own API keys" ON api_keys
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own API keys" ON api_keys
  FOR UPDATE USING (auth.uid() = user_id);

-- Comments
COMMENT ON TABLE api_keys IS 'Simple API keys for user authentication';
COMMENT ON COLUMN api_keys.api_key IS 'Simple random string - no hashing, no prefixes, no complexity';
