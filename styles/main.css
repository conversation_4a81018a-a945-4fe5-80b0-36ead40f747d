@tailwind base;
@tailwind components;
@tailwind utilities;

/* Voice Interface CSS Variables */
:root {
  /* Light theme colors */
  --primary: #3b82f6;
  --primary-light: #dbeafe;
  --primary-dark: #1d4ed8;
  --card-bg: #ffffff;
  --card-border: #e5e7eb;
  --settings-bg: #f9fafb;
  --settings-border: #e5e7eb;
  --settings-text: #374151;
  --input-bg: #ffffff;
  --input-border: #d1d5db;
  --input-text: #374151;
  --text-dark: #111827;
  --text-light: #6b7280;
}

.dark {
  /* Dark theme colors */
  --primary: #60a5fa;
  --primary-light: #1e3a8a;
  --primary-dark: #93c5fd;
  --card-bg: #1f2937;
  --card-border: #374151;
  --settings-bg: #111827;
  --settings-border: #374151;
  --settings-text: #d1d5db;
  --input-bg: #374151;
  --input-border: #4b5563;
  --input-text: #f9fafb;
  --text-dark: #f9fafb;
  --text-light: #9ca3af;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

*:focus:not(ol) {
  @apply outline-none ring-2 ring-pink-500 ring-opacity-50;
}

html {
  height: 100%;
  box-sizing: border-box;
  touch-action: manipulation;
  font-feature-settings:
    'case' 1,
    'rlig' 1,
    'calt' 0;
}

html,
body {
  font-family: -apple-system, system-ui, BlinkMacSystemFont, 'Helvetica Neue',
    'Helvetica', sans-serif;
  text-rendering: optimizeLegibility;
  -moz-osx-font-smoothing: grayscale;
  @apply text-white bg-zinc-800 antialiased;
}

body {
  position: relative;
  min-height: 100%;
  margin: 0;
}

a {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

p a {
  @apply hover:underline;
}

.animated {
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}

.height-screen-helper {
  min-height: calc(100vh - 80px);
}
