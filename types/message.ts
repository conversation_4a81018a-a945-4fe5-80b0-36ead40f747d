export interface Message {
  id: string;
  text: string;
  timestamp: string;
  role: 'user' | 'assistant';
  spoken?: boolean;
  ai_teammate?: string;
}

export interface AITeammate {
  fullName: string;
  jobTitle: string;
  avatarUrl: string;
  voice: string;
}

// AI Teammates with their information
export const AI_TEAMMATES: Record<string, AITeammate> = {
  'sarah': {
    fullName: '<PERSON>',
    jobTitle: 'AI Business Analyst',
    avatarUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    voice: 'Microsoft Linda - English (Canada)'
  },
  'jordan': {
    fullName: '<PERSON>',
    jobTitle: 'AI Project Manager',
    avatarUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    voice: 'Google UK English Male'
  },
  'alex': {
    fullName: '<PERSON>',
    jobTitle: 'AI Architect',
    avatarUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    voice: 'Microsoft David - English (United States)'
  },
  'system': {
    fullName: 'System Assistant',
    jobTitle: 'AI Assistant',
    avatarUrl: 'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=150&h=150&fit=crop&crop=face',
    voice: 'Google UK English Male'
  }
} as const;

export type AITeammateKey = keyof typeof AI_TEAMMATES;
