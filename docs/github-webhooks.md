# GitHub Webhooks Implementation

This document describes the GitHub webhook implementation for receiving real-time data from GitHub repositories.

## Overview

The GitHub webhook system allows the application to receive real-time notifications when events occur in GitHub repositories where the GitHub App is installed. This enables features like:

- Real-time repository activity monitoring
- Automated issue and pull request tracking
- Push event notifications
- Installation management

## Architecture

### Components

1. **Webhook Endpoint**: `/api/github/webhooks/route.ts`
   - Receives GitHub webhook events
   - Verifies webhook signatures for security
   - Routes events to appropriate handlers

2. **Event Handlers**: `utils/github/event-handlers.ts`
   - Processes different types of GitHub events
   - Saves relevant data to the database

3. **Webhook Verification**: `utils/github/webhook-verification.ts`
   - Validates GitHub webhook signatures using HMAC-SHA256
   - Prevents unauthorized webhook calls

4. **Database Integration**: `utils/supabase/github.ts`
   - Stores GitHub data in Supabase tables
   - Manages installations, repositories, and events

5. **Authentication**: `utils/github/auth.ts`
   - Handles GitHub App authentication
   - Generates JWT tokens and installation access tokens

## Database Schema

The implementation creates the following tables:

### `github_installations`
Stores GitHub App installation data:
- `installation_id`: Unique GitHub installation ID
- `account_login`: GitHub account name (user or organization)
- `account_type`: 'User' or 'Organization'
- `permissions`: JSON object with app permissions
- `repository_selection`: 'all' or 'selected'

### `github_repositories`
Stores repository information:
- `repository_id`: Unique GitHub repository ID
- `installation_id`: Reference to installation
- `full_name`: Repository full name (owner/repo)
- `private`: Whether repository is private
- `owner_login`: Repository owner

### `github_push_events`
Stores push event data:
- `repository_id`: Repository where push occurred
- `ref`: Git reference (branch/tag)
- `commit_count`: Number of commits in push
- `pusher_name`: Person who pushed

### `github_pull_request_events`
Stores pull request events:
- `pull_request_number`: PR number
- `action`: Event action (opened, closed, etc.)
- `title`: PR title
- `state`: PR state (open, closed, draft)

### `github_issue_events`
Stores issue events:
- `issue_number`: Issue number
- `action`: Event action (opened, closed, etc.)
- `title`: Issue title
- `labels`: Array of label names

## Setup Instructions

### 1. Create GitHub App

1. Go to GitHub Settings > Developer settings > GitHub Apps
2. Click "New GitHub App"
3. Fill in the required information:
   - **App name**: Your app name
   - **Homepage URL**: Your application URL
   - **Webhook URL**: `https://yourdomain.com/api/github/webhooks`
   - **Webhook secret**: Generate a secure random string

### 2. Configure Permissions

Grant the following permissions to your GitHub App:
- **Repository permissions**:
  - Contents: Read
  - Issues: Read
  - Metadata: Read
  - Pull requests: Read
- **Organization permissions**:
  - Members: Read (optional)

### 3. Subscribe to Events

Enable the following webhook events:
- `installation`
- `installation_repositories`
- `push`
- `pull_request`
- `issues`
- `issue_comment` (optional)

### 4. Secret Management with Doppler

This implementation uses Doppler for secure secret management. You have two options:

#### Option A: Use Doppler (Recommended)

1. Set up your Doppler token:
```bash
DOPPLER_TOKEN=your_doppler_token
```

2. Add secrets to your Doppler project (`ai-sdlc`, config: `prd`):
```bash
# GitHub App Configuration
GITHUB_APP_ID=your_app_id
GITHUB_CLIENT_ID=your_client_id
GITHUB_CLIENT_SECRET=your_client_secret
GITHUB_PRIVATE_KEY=your_private_key_base64_encoded
GITHUB_WEBHOOK_SECRET=your_webhook_secret

# GitHub OAuth (optional, for user authentication)
SUPABASE_AUTH_EXTERNAL_GITHUB_CLIENT_ID=your_oauth_client_id
SUPABASE_AUTH_EXTERNAL_GITHUB_SECRET=your_oauth_secret

# Supabase (optional, if not using environment variables)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

#### Option B: Environment Variables (Fallback)

If Doppler is not available, the system will fall back to environment variables in your `.env.local` file.

**Note**: The private key should be base64 encoded. You can encode it using:
```bash
cat your-private-key.pem | base64 -w 0
```

### 5. Database Migration

Run the database migration to create the required tables:

```bash
npm run supabase:push
```

Or if using local development:
```bash
npm run supabase:reset
```

## Supported Events

### Installation Events
- `installation.created`: App installed on account
- `installation.deleted`: App uninstalled
- `installation.suspend`: App suspended
- `installation.unsuspend`: App unsuspended

### Repository Events
- `installation_repositories.added`: Repositories added to installation
- `installation_repositories.removed`: Repositories removed from installation

### Push Events
- `push`: Code pushed to repository

### Pull Request Events
- `pull_request.opened`: New pull request created
- `pull_request.closed`: Pull request closed/merged
- `pull_request.synchronize`: Pull request updated with new commits

### Issue Events
- `issues.opened`: New issue created
- `issues.closed`: Issue closed
- `issues.reopened`: Issue reopened

## Security

### Webhook Verification
All incoming webhooks are verified using HMAC-SHA256 signatures to ensure they come from GitHub. The verification process:

1. GitHub sends a signature in the `x-hub-signature-256` header
2. The webhook handler calculates the expected signature using the webhook secret
3. Signatures are compared using timing-safe comparison to prevent timing attacks

### Database Security
- Row Level Security (RLS) is enabled on all tables
- Authenticated users can read GitHub data
- Only the service role can write data (used by webhook handlers)

## Testing

### Local Development
For local testing, you can use ngrok to expose your local server:

```bash
# Install ngrok
npm install -g ngrok

# Expose local port
ngrok http 3000

# Update your GitHub App webhook URL to the ngrok URL
# Example: https://abc123.ngrok.io/api/github/webhooks
```

### Webhook Testing
GitHub provides a webhook testing interface in your app settings where you can:
- View recent webhook deliveries
- Redeliver failed webhooks
- Test webhook endpoints

## Monitoring

### Logs
The webhook handler provides detailed logging:
- ✅ Successful events
- ❌ Error events
- 📨 Incoming webhook events
- 🔧 Installation changes

### Health Check
The webhook endpoint provides a health check at:
```
GET /api/github/webhooks
```

Returns:
```json
{
  "status": "ok",
  "service": "github-webhook-handler",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "endpoint": "/api/github/webhooks"
}
```

## Troubleshooting

### Common Issues

1. **Webhook signature verification fails**
   - Check that `GITHUB_WEBHOOK_SECRET` matches your GitHub App settings
   - Ensure the secret doesn't have extra whitespace

2. **Private key errors**
   - Ensure the private key is properly base64 encoded
   - Check that the key includes the full PEM format with headers

3. **Database connection errors**
   - Verify `SUPABASE_SERVICE_ROLE_KEY` is set correctly
   - Ensure the database migration has been applied

4. **Installation not found**
   - Check that the GitHub App is properly installed on the repository
   - Verify the installation ID in the database

### Debug Mode
Enable debug logging by setting:
```bash
NODE_ENV=development
```

This will provide more detailed logging for troubleshooting.
