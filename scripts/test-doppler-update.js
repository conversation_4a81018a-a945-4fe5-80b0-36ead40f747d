#!/usr/bin/env node

/**
 * Test Doppler secret update functionality
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env.local') });
import dopplerConfig from '../utils/github/doppler-config.ts'

async function testDopplerUpdate() {
  console.log('🧪 Testing Doppler Secret Update');
  console.log('================================');

  try {
    // Import the doppler config


    console.log('✅ Doppler config loaded');

    // Test updating a secret
    console.log('📝 Testing secret update...');
    await dopplerConfig.updateSecret('TEST_SECRET', 'test_value_' + Date.now());

    console.log('✅ Secret update successful!');

    // Test setting an installation ID
    console.log('📝 Testing installation ID update...');
    await dopplerConfig.setInstallationId('infinisoft-inc', '70009309');

    console.log('✅ Installation ID update successful!');

    // Test validation
    console.log('📝 Testing configuration validation...');
    const validation = await dopplerConfig.validateConfiguration();

    console.log('Validation result:', validation);

    if (validation.valid) {
      console.log('✅ All Doppler operations working correctly!');
    } else {
      console.log('⚠️ Some validation issues:', validation.errors);
    }

  } catch (error) {
    console.error('❌ Doppler test failed:', error.message);

    if (error.message.includes('Unauthorized')) {
      console.log('');
      console.log('💡 Fix: Check your DOPPLER_TOKEN in .env.local');
      console.log('   Get a new token from: https://dashboard.doppler.com/');
    }
  }
}

if (require.main === module) {
  testDopplerUpdate().catch(console.error);
}

module.exports = { testDopplerUpdate };
