#!/usr/bin/env node

/**
 * Apply GitHub webhook migration directly via Supabase API
 */

const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '../.env.local') });

// Migration SQL
const migrationSQL = `
/**
 * GitHub Webhook Tables
 * Tables to store GitHub webhook events and installation data
 */

-- GitHub App Installations
CREATE TABLE IF NOT EXISTS github_installations (
  id bigserial primary key,
  installation_id bigint unique not null,
  account_id bigint not null,
  account_login text not null,
  account_type text not null check (account_type in ('User', 'Organization')),
  permissions jsonb not null default '{}',
  repository_selection text not null check (repository_selection in ('all', 'selected')),
  access_token text,
  expires_at timestamp with time zone,
  created_at timestamp with time zone not null,
  updated_at timestamp with time zone not null,
  inserted_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Indexes for github_installations
CREATE INDEX IF NOT EXISTS idx_github_installations_installation_id ON github_installations(installation_id);
CREATE INDEX IF NOT EXISTS idx_github_installations_account_login ON github_installations(account_login);
CREATE INDEX IF NOT EXISTS idx_github_installations_account_type ON github_installations(account_type);

-- GitHub Repositories
CREATE TABLE IF NOT EXISTS github_repositories (
  id bigserial primary key,
  repository_id bigint unique not null,
  installation_id bigint references github_installations(installation_id) on delete cascade,
  name text not null,
  full_name text not null,
  private boolean not null default false,
  owner_login text not null,
  owner_type text not null,
  description text,
  default_branch text not null default 'main',
  html_url text not null,
  clone_url text not null,
  ssh_url text not null,
  created_at timestamp with time zone not null,
  updated_at timestamp with time zone not null,
  pushed_at timestamp with time zone,
  inserted_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Indexes for github_repositories
CREATE INDEX IF NOT EXISTS idx_github_repositories_repository_id ON github_repositories(repository_id);
CREATE INDEX IF NOT EXISTS idx_github_repositories_installation_id ON github_repositories(installation_id);
CREATE INDEX IF NOT EXISTS idx_github_repositories_full_name ON github_repositories(full_name);
CREATE INDEX IF NOT EXISTS idx_github_repositories_owner_login ON github_repositories(owner_login);

-- GitHub Push Events
CREATE TABLE IF NOT EXISTS github_push_events (
  id bigserial primary key,
  repository_id bigint not null,
  repository_name text not null,
  ref text not null,
  before_sha text not null,
  after_sha text not null,
  commit_count integer not null default 0,
  pusher_name text not null,
  pusher_email text not null,
  sender_login text not null,
  installation_id bigint,
  created_at timestamp with time zone not null,
  inserted_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Indexes for github_push_events
CREATE INDEX IF NOT EXISTS idx_github_push_events_repository_id ON github_push_events(repository_id);
CREATE INDEX IF NOT EXISTS idx_github_push_events_repository_name ON github_push_events(repository_name);
CREATE INDEX IF NOT EXISTS idx_github_push_events_ref ON github_push_events(ref);
CREATE INDEX IF NOT EXISTS idx_github_push_events_sender_login ON github_push_events(sender_login);
CREATE INDEX IF NOT EXISTS idx_github_push_events_created_at ON github_push_events(created_at);

-- GitHub Pull Request Events
CREATE TABLE IF NOT EXISTS github_pull_request_events (
  id bigserial primary key,
  repository_id bigint not null,
  repository_name text not null,
  pull_request_id bigint not null,
  pull_request_number integer not null,
  action text not null,
  title text not null,
  state text not null,
  user_login text not null,
  head_ref text not null,
  base_ref text not null,
  sender_login text not null,
  installation_id bigint,
  created_at timestamp with time zone not null,
  inserted_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Indexes for github_pull_request_events
CREATE INDEX IF NOT EXISTS idx_github_pr_events_repository_id ON github_pull_request_events(repository_id);
CREATE INDEX IF NOT EXISTS idx_github_pr_events_repository_name ON github_pull_request_events(repository_name);
CREATE INDEX IF NOT EXISTS idx_github_pr_events_pr_number ON github_pull_request_events(pull_request_number);
CREATE INDEX IF NOT EXISTS idx_github_pr_events_action ON github_pull_request_events(action);
CREATE INDEX IF NOT EXISTS idx_github_pr_events_state ON github_pull_request_events(state);
CREATE INDEX IF NOT EXISTS idx_github_pr_events_user_login ON github_pull_request_events(user_login);
CREATE INDEX IF NOT EXISTS idx_github_pr_events_created_at ON github_pull_request_events(created_at);

-- GitHub Issue Events
CREATE TABLE IF NOT EXISTS github_issue_events (
  id bigserial primary key,
  repository_id bigint not null,
  repository_name text not null,
  issue_id bigint not null,
  issue_number integer not null,
  action text not null,
  title text not null,
  state text not null,
  user_login text not null,
  labels text[] not null default '{}',
  sender_login text not null,
  installation_id bigint,
  created_at timestamp with time zone not null,
  inserted_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Indexes for github_issue_events
CREATE INDEX IF NOT EXISTS idx_github_issue_events_repository_id ON github_issue_events(repository_id);
CREATE INDEX IF NOT EXISTS idx_github_issue_events_repository_name ON github_issue_events(repository_name);
CREATE INDEX IF NOT EXISTS idx_github_issue_events_issue_number ON github_issue_events(issue_number);
CREATE INDEX IF NOT EXISTS idx_github_issue_events_action ON github_issue_events(action);
CREATE INDEX IF NOT EXISTS idx_github_issue_events_state ON github_issue_events(state);
CREATE INDEX IF NOT EXISTS idx_github_issue_events_user_login ON github_issue_events(user_login);
CREATE INDEX IF NOT EXISTS idx_github_issue_events_created_at ON github_issue_events(created_at);

-- Row Level Security (RLS) policies
ALTER TABLE github_installations ENABLE ROW LEVEL SECURITY;
ALTER TABLE github_repositories ENABLE ROW LEVEL SECURITY;
ALTER TABLE github_push_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE github_pull_request_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE github_issue_events ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Allow authenticated read access" ON github_installations;
DROP POLICY IF EXISTS "Allow authenticated read access" ON github_repositories;
DROP POLICY IF EXISTS "Allow authenticated read access" ON github_push_events;
DROP POLICY IF EXISTS "Allow authenticated read access" ON github_pull_request_events;
DROP POLICY IF EXISTS "Allow authenticated read access" ON github_issue_events;

DROP POLICY IF EXISTS "Allow service role full access" ON github_installations;
DROP POLICY IF EXISTS "Allow service role full access" ON github_repositories;
DROP POLICY IF EXISTS "Allow service role full access" ON github_push_events;
DROP POLICY IF EXISTS "Allow service role full access" ON github_pull_request_events;
DROP POLICY IF EXISTS "Allow service role full access" ON github_issue_events;

-- Create new policies
CREATE POLICY "Allow authenticated read access" ON github_installations FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Allow authenticated read access" ON github_repositories FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Allow authenticated read access" ON github_push_events FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Allow authenticated read access" ON github_pull_request_events FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Allow authenticated read access" ON github_issue_events FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Allow service role full access" ON github_installations FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Allow service role full access" ON github_repositories FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Allow service role full access" ON github_push_events FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Allow service role full access" ON github_pull_request_events FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Allow service role full access" ON github_issue_events FOR ALL USING (auth.role() = 'service_role');

-- Add GitHub tables to realtime publication for real-time updates
ALTER PUBLICATION supabase_realtime ADD TABLE github_installations;
ALTER PUBLICATION supabase_realtime ADD TABLE github_repositories;
ALTER PUBLICATION supabase_realtime ADD TABLE github_push_events;
ALTER PUBLICATION supabase_realtime ADD TABLE github_pull_request_events;
ALTER PUBLICATION supabase_realtime ADD TABLE github_issue_events;
`;

async function applyMigrationAPI() {
  console.log('🚀 Applying GitHub webhook migration via Supabase API...');
  
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  
  if (!supabaseUrl || !serviceKey) {
    console.error('❌ Missing Supabase environment variables');
    console.log('Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
    process.exit(1);
  }

  try {
    const { createClient } = require('@supabase/supabase-js');
    const supabase = createClient(supabaseUrl, serviceKey);

    console.log('📊 Creating tables directly...');

    // Create tables one by one using direct SQL
    const tableStatements = [
      `CREATE TABLE IF NOT EXISTS github_installations (
        id bigserial primary key,
        installation_id bigint unique not null,
        account_id bigint not null,
        account_login text not null,
        account_type text not null check (account_type in ('User', 'Organization')),
        permissions jsonb not null default '{}',
        repository_selection text not null check (repository_selection in ('all', 'selected')),
        access_token text,
        expires_at timestamp with time zone,
        created_at timestamp with time zone not null,
        updated_at timestamp with time zone not null,
        inserted_at timestamp with time zone default timezone('utc'::text, now()) not null
      )`,

      `CREATE TABLE IF NOT EXISTS github_repositories (
        id bigserial primary key,
        repository_id bigint unique not null,
        installation_id bigint,
        name text not null,
        full_name text not null,
        private boolean not null default false,
        owner_login text not null,
        owner_type text not null,
        description text,
        default_branch text not null default 'main',
        html_url text not null,
        clone_url text not null,
        ssh_url text not null,
        created_at timestamp with time zone not null,
        updated_at timestamp with time zone not null,
        pushed_at timestamp with time zone,
        inserted_at timestamp with time zone default timezone('utc'::text, now()) not null
      )`,

      `CREATE TABLE IF NOT EXISTS github_push_events (
        id bigserial primary key,
        repository_id bigint not null,
        repository_name text not null,
        ref text not null,
        before_sha text not null,
        after_sha text not null,
        commit_count integer not null default 0,
        pusher_name text not null,
        pusher_email text not null,
        sender_login text not null,
        installation_id bigint,
        created_at timestamp with time zone not null,
        inserted_at timestamp with time zone default timezone('utc'::text, now()) not null
      )`,

      `CREATE TABLE IF NOT EXISTS github_pull_request_events (
        id bigserial primary key,
        repository_id bigint not null,
        repository_name text not null,
        pull_request_id bigint not null,
        pull_request_number integer not null,
        action text not null,
        title text not null,
        state text not null,
        user_login text not null,
        head_ref text not null,
        base_ref text not null,
        sender_login text not null,
        installation_id bigint,
        created_at timestamp with time zone not null,
        inserted_at timestamp with time zone default timezone('utc'::text, now()) not null
      )`,

      `CREATE TABLE IF NOT EXISTS github_issue_events (
        id bigserial primary key,
        repository_id bigint not null,
        repository_name text not null,
        issue_id bigint not null,
        issue_number integer not null,
        action text not null,
        title text not null,
        state text not null,
        user_login text not null,
        labels text[] not null default '{}',
        sender_login text not null,
        installation_id bigint,
        created_at timestamp with time zone not null,
        inserted_at timestamp with time zone default timezone('utc'::text, now()) not null
      )`
    ];

    // Execute each table creation
    for (let i = 0; i < tableStatements.length; i++) {
      const statement = tableStatements[i];
      console.log(`Creating table ${i + 1}/5...`);

      const { error } = await supabase.rpc('exec_sql', { sql: statement });
      if (error) {
        console.log(`⚠️ Table ${i + 1} warning: ${error.message}`);
      } else {
        console.log(`✅ Table ${i + 1} created`);
      }
    }

    console.log('✅ All tables created!');

    // Verify tables were created
    console.log('🔍 Verifying tables...');
    const { data: tables, error: tableError } = await supabase
      .from('github_installations')
      .select('count')
      .limit(1);

    if (!tableError) {
      console.log('✅ GitHub webhook tables created successfully!');
      
      // Test each table
      const testTables = [
        'github_installations',
        'github_repositories', 
        'github_push_events',
        'github_pull_request_events',
        'github_issue_events'
      ];

      for (const table of testTables) {
        const { error: testError } = await supabase
          .from(table)
          .select('count')
          .limit(1);
        
        if (!testError) {
          console.log(`   ✅ ${table}`);
        } else {
          console.log(`   ❌ ${table}: ${testError.message}`);
        }
      }
      
      console.log('');
      console.log('🎉 Database setup complete!');
      console.log('');
      console.log('Next steps:');
      console.log('1. npm run dev');
      console.log('2. npm run test:complete');
      
    } else {
      console.log('❌ Table verification failed:', tableError.message);
    }

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    
    console.log('');
    console.log('📋 Manual fallback:');
    console.log('Go to: https://supabase.com/dashboard/project/skovzcqcmwdzgelqhwuq/sql');
    console.log('Copy and paste this SQL:');
    console.log('');
    console.log(migrationSQL);
    
    process.exit(1);
  }
}

if (require.main === module) {
  applyMigrationAPI().catch(console.error);
}

module.exports = { applyMigrationAPI, migrationSQL };
