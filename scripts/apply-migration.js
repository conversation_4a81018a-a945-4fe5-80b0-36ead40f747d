#!/usr/bin/env node

/**
 * Apply GitHub webhook migration directly to Supabase
 * This bypasses CLI connection issues
 */

const fs = require('fs');
const path = require('path');

async function applyMigration() {
  console.log('🔧 Applying GitHub webhook migration...');
  
  // Read the migration file
  const migrationPath = path.join(__dirname, '../supabase/migrations/20241201000000_github_webhooks.sql');
  
  if (!fs.existsSync(migrationPath)) {
    console.error('❌ Migration file not found:', migrationPath);
    process.exit(1);
  }
  
  const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
  
  console.log('📋 Migration SQL loaded successfully');
  console.log('');
  console.log('🎯 Next steps:');
  console.log('1. Go to your Supabase dashboard: https://supabase.com/dashboard/project/skovzcqcmwdzgelqhwuq/sql');
  console.log('2. Click "New query"');
  console.log('3. Copy and paste the SQL below:');
  console.log('4. Click "Run"');
  console.log('');
  console.log('=' .repeat(80));
  console.log('SQL MIGRATION TO COPY:');
  console.log('=' .repeat(80));
  console.log('');
  console.log(migrationSQL);
  console.log('');
  console.log('=' .repeat(80));
  console.log('');
  console.log('✅ After running the SQL, your GitHub webhook tables will be ready!');
}

if (require.main === module) {
  applyMigration().catch(console.error);
}

module.exports = { applyMigration };
