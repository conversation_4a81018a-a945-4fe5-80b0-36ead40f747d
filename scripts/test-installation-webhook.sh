#!/bin/bash

# Test installation webhook with proper payload
echo "🧪 Testing Installation Webhook"
echo "==============================="

WEBHOOK_URL="http://localhost:3001/api/github/webhooks"
WEBHOOK_SECRET="github_webhook_secret_2024_secure_key_for_ai_sdlc_app"

# Function to generate signature
generate_signature() {
    local payload="$1"
    local secret="$2"
    echo -n "$payload" | openssl dgst -sha256 -hmac "$secret" | sed 's/^.* //'
}

echo ""
echo "🔧 Testing installation created webhook..."

INSTALLATION_PAYLOAD='{
  "action": "created",
  "installation": {
    "id": ********,
    "account": {
      "login": "Infinisoft-inc",
      "id": 12345,
      "type": "Organization"
    },
    "repository_selection": "selected",
    "access_tokens_url": "https://api.github.com/app/installations/********/access_tokens",
    "repositories_url": "https://api.github.com/installation/repositories",
    "html_url": "https://github.com/organizations/Infinisoft-inc/settings/installations/********",
    "app_id": 123456,
    "target_id": 12345,
    "target_type": "Organization",
    "permissions": {
      "contents": "read",
      "issues": "write",
      "pull_requests": "write"
    },
    "events": ["push", "pull_request", "issues"],
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z",
    "single_file_name": null,
    "app_slug": "ai-sdlc-app"
  },
  "repositories": [
    {
      "id": *********,
      "name": "test-repo",
      "full_name": "Infinisoft-inc/test-repo",
      "owner": {
        "login": "Infinisoft-inc",
        "id": 12345,
        "type": "Organization"
      },
      "private": false,
      "html_url": "https://github.com/Infinisoft-inc/test-repo",
      "description": "Test repository",
      "default_branch": "main",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z",
      "pushed_at": "2024-01-01T00:00:00Z",
      "clone_url": "https://github.com/Infinisoft-inc/test-repo.git",
      "ssh_url": "**************:Infinisoft-inc/test-repo.git"
    }
  ],
  "sender": {
    "login": "mouimet-infinisoft",
    "id": 67890,
    "type": "User"
  }
}'

SIGNATURE="sha256=$(generate_signature "$INSTALLATION_PAYLOAD" "$WEBHOOK_SECRET")"

echo "Sending installation webhook..."
curl -s -X POST "$WEBHOOK_URL" \
  -H "Content-Type: application/json" \
  -H "X-GitHub-Event: installation" \
  -H "X-GitHub-Delivery: test-$(date +%s)" \
  -H "X-Hub-Signature-256: $SIGNATURE" \
  -H "User-Agent: GitHub-Hookshot/test" \
  -d "$INSTALLATION_PAYLOAD"

echo ""
echo ""
echo "🎉 Installation webhook test completed!"
echo "📊 Check the logs in your terminal running 'npm run dev'"
