#!/bin/bash

# GitHub Webhook Test with CORRECT signature
echo "🧪 Testing GitHub Webhook with FIXED signature"
echo "=============================================="

WEBHOOK_URL="http://localhost:3001/api/github/webhooks"
WEBHOOK_SECRET="github_webhook_secret_2024_secure_key_for_ai_sdlc_app"

# Function to generate signature
generate_signature() {
    local payload="$1"
    local secret="$2"
    echo -n "$payload" | openssl dgst -sha256 -hmac "$secret" | sed 's/^.* //'
}

echo ""
echo "1. 🏥 Testing health check..."
curl -s -X GET "$WEBHOOK_URL"

echo ""
echo ""
echo "2. 🏓 Testing ping webhook with CORRECT signature..."

PING_PAYLOAD='{"zen":"Non-blocking is better than blocking.","hook_id":12345678}'

SIGNATURE="sha256=$(generate_signature "$PING_PAYLOAD" "$WEBHOOK_SECRET")"

echo "Payload: $PING_PAYLOAD"
echo "Secret: $WEBHOOK_SECRET"
echo "Signature: $SIGNATURE"
echo ""

curl -s -X POST "$WEBHOOK_URL" \
  -H "Content-Type: application/json" \
  -H "X-GitHub-Event: ping" \
  -H "X-GitHub-Delivery: test-$(date +%s)" \
  -H "X-Hub-Signature-256: $SIGNATURE" \
  -H "User-Agent: GitHub-Hookshot/test" \
  -d "$PING_PAYLOAD"

echo ""
echo ""
echo "3. 📤 Testing push webhook with CORRECT signature..."

PUSH_PAYLOAD='{"ref":"refs/heads/main","repository":{"id":123,"name":"test-repo","full_name":"user/test-repo"},"pusher":{"name":"testuser","email":"<EMAIL>"},"sender":{"login":"testuser"},"commits":[{"id":"abc123","message":"Test commit"}]}'

SIGNATURE="sha256=$(generate_signature "$PUSH_PAYLOAD" "$WEBHOOK_SECRET")"

curl -s -X POST "$WEBHOOK_URL" \
  -H "Content-Type: application/json" \
  -H "X-GitHub-Event: push" \
  -H "X-GitHub-Delivery: test-$(date +%s)" \
  -H "X-Hub-Signature-256: $SIGNATURE" \
  -H "User-Agent: GitHub-Hookshot/test" \
  -d "$PUSH_PAYLOAD"

echo ""
echo ""
echo "🎉 Webhook tests completed!"
echo ""
echo "📊 Check the logs in your terminal running 'npm run dev'"
echo "🌐 Visit the dashboard: http://localhost:3001/github"
echo ""
echo "🔑 Your webhook secret is: $WEBHOOK_SECRET"
echo "📋 Use this secret when configuring your GitHub App webhook!"
