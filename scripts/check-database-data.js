#!/usr/bin/env node

/**
 * Check if data was actually saved to the database
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env.local') });

async function checkDatabaseData() {
  console.log('🔍 Checking Database Data');
  console.log('========================');
  
  try {
    const { createClient } = require('@supabase/supabase-js');
    
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (!supabaseUrl || !serviceKey) {
      console.error('❌ Missing Supabase environment variables');
      return;
    }
    
    const supabase = createClient(supabaseUrl, serviceKey);
    
    // Check installations
    console.log('📋 Checking github_installations table...');
    const { data: installations, error: installError } = await supabase
      .from('github_installations')
      .select('*')
      .order('inserted_at', { ascending: false })
      .limit(5);
    
    if (installError) {
      console.error('❌ Error querying installations:', installError);
    } else {
      console.log(`✅ Found ${installations.length} installation(s):`);
      installations.forEach((install, i) => {
        console.log(`  ${i + 1}. ID: ${install.installation_id}, Account: ${install.account_login}, Type: ${install.account_type}`);
      });
    }
    
    console.log('');
    
    // Check repositories
    console.log('📁 Checking github_repositories table...');
    const { data: repositories, error: repoError } = await supabase
      .from('github_repositories')
      .select('*')
      .order('inserted_at', { ascending: false })
      .limit(5);
    
    if (repoError) {
      console.error('❌ Error querying repositories:', repoError);
    } else {
      console.log(`✅ Found ${repositories.length} repository(ies):`);
      repositories.forEach((repo, i) => {
        console.log(`  ${i + 1}. ID: ${repo.repository_id}, Name: ${repo.full_name}, Owner: ${repo.owner_login}`);
      });
    }
    
    console.log('');
    
    // Check push events
    console.log('📤 Checking github_push_events table...');
    const { data: pushEvents, error: pushError } = await supabase
      .from('github_push_events')
      .select('*')
      .order('inserted_at', { ascending: false })
      .limit(5);
    
    if (pushError) {
      console.error('❌ Error querying push events:', pushError);
    } else {
      console.log(`✅ Found ${pushEvents.length} push event(s):`);
      pushEvents.forEach((event, i) => {
        console.log(`  ${i + 1}. Repo: ${event.repository_name}, Ref: ${event.ref}, Pusher: ${event.pusher_name}`);
      });
    }
    
    console.log('');
    
    // Check table structure
    console.log('🏗️ Checking table structure...');
    const { data: tableInfo, error: tableError } = await supabase
      .rpc('exec_sql', { 
        sql: `SELECT column_name, data_type, is_nullable 
              FROM information_schema.columns 
              WHERE table_name = 'github_installations' 
              ORDER BY ordinal_position;` 
      });
    
    if (tableError) {
      console.log('⚠️ Could not check table structure (this is normal)');
    } else {
      console.log('✅ github_installations table structure:');
      if (tableInfo && Array.isArray(tableInfo)) {
        tableInfo.forEach(col => {
          console.log(`  - ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
        });
      }
    }
    
  } catch (error) {
    console.error('❌ Database check failed:', error.message);
  }
}

if (require.main === module) {
  checkDatabaseData().catch(console.error);
}

module.exports = { checkDatabaseData };
