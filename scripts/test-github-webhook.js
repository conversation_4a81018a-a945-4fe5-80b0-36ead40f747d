#!/usr/bin/env node

/**
 * GitHub Webhook Test Script
 * Tests the GitHub webhook endpoint with sample payloads
 */

const crypto = require('crypto');

// Use dynamic import for node-fetch
let fetch;
(async () => {
  const nodeFetch = await import('node-fetch');
  fetch = nodeFetch.default;
})();

// Configuration
const WEBHOOK_URL = process.env.WEBHOOK_URL || 'http://localhost:3001/api/github/webhooks';
const WEBHOOK_SECRET = process.env.GITHUB_WEBHOOK_SECRET || 'test-secret';

/**
 * Generate GitHub webhook signature
 */
function generateSignature(payload, secret) {
  const signature = crypto
    .createHmac('sha256', secret)
    .update(payload, 'utf8')
    .digest('hex');
  
  return `sha256=${signature}`;
}

/**
 * Send webhook test
 */
async function sendWebhook(event, payload) {
  const body = JSON.stringify(payload);
  const signature = generateSignature(body, WEBHOOK_SECRET);
  
  console.log(`🚀 Sending ${event} webhook to ${WEBHOOK_URL}`);
  
  try {
    const response = await fetch(WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-GitHub-Event': event,
        'X-GitHub-Delivery': `test-${Date.now()}`,
        'X-Hub-Signature-256': signature,
        'User-Agent': 'GitHub-Hookshot/test'
      },
      body
    });
    
    const responseText = await response.text();
    
    if (response.ok) {
      console.log(`✅ ${event} webhook successful:`, response.status);
      console.log('Response:', responseText);
    } else {
      console.log(`❌ ${event} webhook failed:`, response.status);
      console.log('Response:', responseText);
    }
    
    console.log('---');
    
  } catch (error) {
    console.error(`❌ Error sending ${event} webhook:`, error.message);
    console.log('---');
  }
}

/**
 * Test payloads
 */
const testPayloads = {
  ping: {
    zen: "Non-blocking is better than blocking.",
    hook_id: 12345678,
    hook: {
      type: "Repository",
      id: 12345678,
      name: "web",
      active: true,
      events: ["push", "pull_request"],
      config: {
        content_type: "json",
        insecure_ssl: "0",
        url: WEBHOOK_URL
      }
    },
    repository: {
      id: 35129377,
      name: "public-repo",
      full_name: "baxterthehacker/public-repo",
      owner: {
        login: "baxterthehacker",
        id: 6752317,
        avatar_url: "https://avatars.githubusercontent.com/u/6752317?v=3",
        type: "User",
        site_admin: false
      },
      private: false,
      html_url: "https://github.com/baxterthehacker/public-repo",
      description: "",
      default_branch: "master"
    },
    sender: {
      login: "baxterthehacker",
      id: 6752317,
      avatar_url: "https://avatars.githubusercontent.com/u/6752317?v=3",
      type: "User",
      site_admin: false
    }
  },

  installation: {
    action: "created",
    installation: {
      id: 1,
      account: {
        login: "github",
        id: 1,
        type: "Organization"
      },
      repository_selection: "selected",
      access_tokens_url: "https://api.github.com/installations/1/access_tokens",
      repositories_url: "https://api.github.com/installation/repositories",
      html_url: "https://github.com/organizations/github/settings/installations/1",
      app_id: 1,
      target_id: 1,
      target_type: "Organization",
      permissions: {
        issues: "write",
        contents: "read"
      },
      events: ["push", "pull_request"],
      created_at: "2024-01-01T00:00:00Z",
      updated_at: "2024-01-01T00:00:00Z",
      single_file_name: null,
      app_slug: "test-app"
    },
    repositories: [
      {
        id: 1296269,
        name: "Hello-World",
        full_name: "octocat/Hello-World",
        owner: {
          login: "octocat",
          id: 1,
          type: "User"
        },
        private: false,
        html_url: "https://github.com/octocat/Hello-World",
        description: "This your first repo!",
        default_branch: "master",
        created_at: "2011-01-26T19:01:12Z",
        updated_at: "2011-01-26T19:14:43Z",
        pushed_at: "2011-01-26T19:06:43Z",
        clone_url: "https://github.com/octocat/Hello-World.git",
        ssh_url: "**************:octocat/Hello-World.git"
      }
    ],
    sender: {
      login: "octocat",
      id: 1,
      type: "User"
    }
  },

  push: {
    ref: "refs/heads/main",
    before: "95790bf891e76f994c616e1ea6c4e4b536b5a8b3",
    after: "da1560886d4f094c3e6c9ef40349f7d38b5d27d7",
    created: false,
    deleted: false,
    forced: false,
    base_ref: null,
    compare: "https://github.com/baxterthehacker/public-repo/compare/95790bf891e7...da1560886d4f",
    commits: [
      {
        id: "da1560886d4f094c3e6c9ef40349f7d38b5d27d7",
        tree_id: "f9d2a07e9488b91af2641b26b9407fe22a451433",
        distinct: true,
        message: "Update README.md",
        timestamp: "2024-01-01T00:00:00Z",
        url: "https://github.com/baxterthehacker/public-repo/commit/da1560886d4f094c3e6c9ef40349f7d38b5d27d7",
        author: {
          name: "baxterthehacker",
          email: "<EMAIL>",
          username: "baxterthehacker"
        },
        committer: {
          name: "baxterthehacker",
          email: "<EMAIL>",
          username: "baxterthehacker"
        },
        added: [],
        removed: [],
        modified: ["README.md"]
      }
    ],
    head_commit: {
      id: "da1560886d4f094c3e6c9ef40349f7d38b5d27d7",
      tree_id: "f9d2a07e9488b91af2641b26b9407fe22a451433",
      distinct: true,
      message: "Update README.md",
      timestamp: "2024-01-01T00:00:00Z",
      url: "https://github.com/baxterthehacker/public-repo/commit/da1560886d4f094c3e6c9ef40349f7d38b5d27d7",
      author: {
        name: "baxterthehacker",
        email: "<EMAIL>",
        username: "baxterthehacker"
      },
      committer: {
        name: "baxterthehacker",
        email: "<EMAIL>",
        username: "baxterthehacker"
      },
      added: [],
      removed: [],
      modified: ["README.md"]
    },
    repository: {
      id: 35129377,
      name: "public-repo",
      full_name: "baxterthehacker/public-repo",
      owner: {
        login: "baxterthehacker",
        id: 6752317,
        type: "User"
      },
      private: false,
      html_url: "https://github.com/baxterthehacker/public-repo",
      description: "",
      default_branch: "master",
      created_at: "2015-05-05T23:40:12Z",
      updated_at: "2015-05-05T23:40:12Z",
      pushed_at: "2015-05-05T23:40:27Z",
      clone_url: "https://github.com/baxterthehacker/public-repo.git",
      ssh_url: "**************:baxterthehacker/public-repo.git"
    },
    pusher: {
      name: "baxterthehacker",
      email: "<EMAIL>"
    },
    sender: {
      login: "baxterthehacker",
      id: 6752317,
      type: "User"
    },
    installation: {
      id: 1
    }
  }
};

/**
 * Main test function
 */
async function runTests() {
  console.log('🧪 GitHub Webhook Test Suite');
  console.log(`📡 Target URL: ${WEBHOOK_URL}`);
  console.log(`🔐 Using webhook secret: ${WEBHOOK_SECRET ? '***' : 'NOT SET'}`);
  console.log('');

  // Test health check first
  try {
    console.log('🏥 Testing health check...');
    const response = await fetch(WEBHOOK_URL, { method: 'GET' });
    const health = await response.json();
    console.log('✅ Health check:', health);
    console.log('---');
  } catch (error) {
    console.error('❌ Health check failed:', error.message);
    console.log('---');
  }

  // Test webhook events
  for (const [event, payload] of Object.entries(testPayloads)) {
    await sendWebhook(event, payload);
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  console.log('🎉 Test suite completed!');
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { sendWebhook, generateSignature, testPayloads };
