#!/usr/bin/env node

/**
 * Complete GitHub Webhook System Test
 * Tests all components: database, webhook endpoint, Doppler, etc.
 */

const fetch = require('node-fetch');
const { createClient } = require('@supabase/supabase-js');

async function testCompleteSetup() {
  console.log('🧪 Complete GitHub Webhook System Test');
  console.log('======================================');
  console.log('');

  const results = {
    environment: false,
    database: false,
    webhook: false,
    doppler: false,
    app: false
  };

  // Test 1: Environment Variables
  console.log('1. 🔧 Testing environment variables...');
  try {
    const requiredEnvs = [
      'NEXT_PUBLIC_SUPABASE_URL',
      'NEXT_PUBLIC_SUPABASE_ANON_KEY',
      'SUPABASE_SERVICE_ROLE_KEY',
      'DOPPLER_TOKEN'
    ];

    const missing = requiredEnvs.filter(env => !process.env[env]);
    
    if (missing.length === 0) {
      console.log('   ✅ All required environment variables present');
      results.environment = true;
    } else {
      console.log(`   ❌ Missing: ${missing.join(', ')}`);
    }
  } catch (error) {
    console.log('   ❌ Environment check failed:', error.message);
  }

  // Test 2: Database Connection
  console.log('');
  console.log('2. 🗄️ Testing Supabase database connection...');
  try {
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    );

    // Test basic connection
    const { data, error } = await supabase
      .from('github_installations')
      .select('count')
      .limit(1);

    if (!error) {
      console.log('   ✅ Database connection successful');
      console.log('   ✅ GitHub webhook tables exist');
      results.database = true;
    } else {
      console.log('   ❌ Database error:', error.message);
      if (error.message.includes('relation "github_installations" does not exist')) {
        console.log('   💡 Run the migration: npm run setup:supabase');
      }
    }
  } catch (error) {
    console.log('   ❌ Database connection failed:', error.message);
  }

  // Test 3: Webhook Endpoint
  console.log('');
  console.log('3. 🌐 Testing webhook endpoint...');
  try {
    const webhookUrl = 'http://localhost:3001/api/github/webhooks';
    
    // Test health check
    const response = await fetch(webhookUrl, {
      method: 'GET',
      timeout: 5000
    });

    if (response.ok) {
      const health = await response.json();
      console.log('   ✅ Webhook endpoint responding');
      console.log(`   ✅ Service: ${health.service}`);
      results.webhook = true;
    } else {
      console.log('   ❌ Webhook endpoint not responding');
      console.log('   💡 Start the app: npm run dev');
    }
  } catch (error) {
    console.log('   ❌ Webhook test failed:', error.message);
    console.log('   💡 Make sure the app is running: npm run dev');
  }

  // Test 4: Doppler Configuration
  console.log('');
  console.log('4. 🔐 Testing Doppler configuration...');
  try {
    const DopplerSDK = require('@dopplerhq/node-sdk');
    const doppler = new DopplerSDK({
      accessToken: process.env.DOPPLER_TOKEN
    });

    const secrets = await doppler.secrets.list('ai-sdlc', 'prd');
    
    const githubSecrets = Object.keys(secrets).filter(key => 
      key.startsWith('GITHUB_')
    );

    if (githubSecrets.length > 0) {
      console.log('   ✅ Doppler connection successful');
      console.log(`   ✅ Found ${githubSecrets.length} GitHub secrets`);
      results.doppler = true;
    } else {
      console.log('   ⚠️ Doppler connected but no GitHub secrets found');
      console.log('   💡 Run: npm run setup:doppler');
    }
  } catch (error) {
    console.log('   ❌ Doppler test failed:', error.message);
    console.log('   💡 Check your DOPPLER_TOKEN');
  }

  // Test 5: App Functionality
  console.log('');
  console.log('5. 🚀 Testing app functionality...');
  try {
    const appUrl = 'http://localhost:3001/github';
    
    const response = await fetch(appUrl, {
      method: 'GET',
      timeout: 5000
    });

    if (response.ok) {
      console.log('   ✅ GitHub dashboard accessible');
      results.app = true;
    } else {
      console.log('   ❌ GitHub dashboard not accessible');
    }
  } catch (error) {
    console.log('   ❌ App test failed:', error.message);
    console.log('   💡 Make sure the app is running: npm run dev');
  }

  // Summary
  console.log('');
  console.log('📊 Test Summary:');
  console.log('================');
  
  const passed = Object.values(results).filter(Boolean).length;
  const total = Object.keys(results).length;
  
  Object.entries(results).forEach(([test, passed]) => {
    const icon = passed ? '✅' : '❌';
    console.log(`${icon} ${test.charAt(0).toUpperCase() + test.slice(1)}`);
  });

  console.log('');
  console.log(`🎯 Score: ${passed}/${total} tests passed`);

  if (passed === total) {
    console.log('');
    console.log('🎉 All tests passed! Your GitHub webhook system is ready!');
    console.log('');
    console.log('Next steps:');
    console.log('1. Create your GitHub App: https://github.com/settings/apps/new');
    console.log('2. Set webhook URL: https://yourdomain.com/api/github/webhooks');
    console.log('3. Install the app on repositories');
    console.log('4. Watch real-time events at: http://localhost:3000/github');
  } else {
    console.log('');
    console.log('🔧 Some tests failed. Follow the suggestions above to fix them.');
    console.log('');
    console.log('Quick fixes:');
    console.log('• Database: npm run setup:supabase');
    console.log('• Doppler: npm run setup:doppler');
    console.log('• App: npm run dev');
  }

  return passed === total;
}

if (require.main === module) {
  testCompleteSetup()
    .then(success => process.exit(success ? 0 : 1))
    .catch(error => {
      console.error('Test failed:', error);
      process.exit(1);
    });
}

module.exports = { testCompleteSetup };
