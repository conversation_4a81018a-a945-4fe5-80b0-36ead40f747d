#!/usr/bin/env node

/**
 * Create GitHub webhook tables directly using Supabase REST API
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env.local') });

async function createTablesDirect() {
  console.log('🚀 Creating GitHub webhook tables directly...');
  
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  
  if (!supabaseUrl || !serviceKey) {
    console.error('❌ Missing Supabase environment variables');
    process.exit(1);
  }

  const fetch = require('node-fetch');
  
  // SQL statements to execute
  const sqlStatements = [
    // Create github_installations table
    `CREATE TABLE IF NOT EXISTS github_installations (
      id bigserial primary key,
      installation_id bigint unique not null,
      account_id bigint not null,
      account_login text not null,
      account_type text not null,
      permissions jsonb not null default '{}',
      repository_selection text not null,
      access_token text,
      expires_at timestamp with time zone,
      created_at timestamp with time zone not null,
      updated_at timestamp with time zone not null,
      inserted_at timestamp with time zone default timezone('utc'::text, now()) not null
    );`,
    
    // Create github_repositories table
    `CREATE TABLE IF NOT EXISTS github_repositories (
      id bigserial primary key,
      repository_id bigint unique not null,
      installation_id bigint,
      name text not null,
      full_name text not null,
      private boolean not null default false,
      owner_login text not null,
      owner_type text not null,
      description text,
      default_branch text not null default 'main',
      html_url text not null,
      clone_url text not null,
      ssh_url text not null,
      created_at timestamp with time zone not null,
      updated_at timestamp with time zone not null,
      pushed_at timestamp with time zone,
      inserted_at timestamp with time zone default timezone('utc'::text, now()) not null
    );`,
    
    // Create github_push_events table
    `CREATE TABLE IF NOT EXISTS github_push_events (
      id bigserial primary key,
      repository_id bigint not null,
      repository_name text not null,
      ref text not null,
      before_sha text not null,
      after_sha text not null,
      commit_count integer not null default 0,
      pusher_name text not null,
      pusher_email text not null,
      sender_login text not null,
      installation_id bigint,
      created_at timestamp with time zone not null,
      inserted_at timestamp with time zone default timezone('utc'::text, now()) not null
    );`,
    
    // Create github_pull_request_events table
    `CREATE TABLE IF NOT EXISTS github_pull_request_events (
      id bigserial primary key,
      repository_id bigint not null,
      repository_name text not null,
      pull_request_id bigint not null,
      pull_request_number integer not null,
      action text not null,
      title text not null,
      state text not null,
      user_login text not null,
      head_ref text not null,
      base_ref text not null,
      sender_login text not null,
      installation_id bigint,
      created_at timestamp with time zone not null,
      inserted_at timestamp with time zone default timezone('utc'::text, now()) not null
    );`,
    
    // Create github_issue_events table
    `CREATE TABLE IF NOT EXISTS github_issue_events (
      id bigserial primary key,
      repository_id bigint not null,
      repository_name text not null,
      issue_id bigint not null,
      issue_number integer not null,
      action text not null,
      title text not null,
      state text not null,
      user_login text not null,
      labels text[] not null default '{}',
      sender_login text not null,
      installation_id bigint,
      created_at timestamp with time zone not null,
      inserted_at timestamp with time zone default timezone('utc'::text, now()) not null
    );`,
    
    // Enable RLS
    `ALTER TABLE github_installations ENABLE ROW LEVEL SECURITY;`,
    `ALTER TABLE github_repositories ENABLE ROW LEVEL SECURITY;`,
    `ALTER TABLE github_push_events ENABLE ROW LEVEL SECURITY;`,
    `ALTER TABLE github_pull_request_events ENABLE ROW LEVEL SECURITY;`,
    `ALTER TABLE github_issue_events ENABLE ROW LEVEL SECURITY;`,
    
    // Create policies
    `CREATE POLICY IF NOT EXISTS "Allow authenticated read access" ON github_installations FOR SELECT USING (auth.role() = 'authenticated');`,
    `CREATE POLICY IF NOT EXISTS "Allow authenticated read access" ON github_repositories FOR SELECT USING (auth.role() = 'authenticated');`,
    `CREATE POLICY IF NOT EXISTS "Allow authenticated read access" ON github_push_events FOR SELECT USING (auth.role() = 'authenticated');`,
    `CREATE POLICY IF NOT EXISTS "Allow authenticated read access" ON github_pull_request_events FOR SELECT USING (auth.role() = 'authenticated');`,
    `CREATE POLICY IF NOT EXISTS "Allow authenticated read access" ON github_issue_events FOR SELECT USING (auth.role() = 'authenticated');`,
    
    `CREATE POLICY IF NOT EXISTS "Allow service role full access" ON github_installations FOR ALL USING (auth.role() = 'service_role');`,
    `CREATE POLICY IF NOT EXISTS "Allow service role full access" ON github_repositories FOR ALL USING (auth.role() = 'service_role');`,
    `CREATE POLICY IF NOT EXISTS "Allow service role full access" ON github_push_events FOR ALL USING (auth.role() = 'service_role');`,
    `CREATE POLICY IF NOT EXISTS "Allow service role full access" ON github_pull_request_events FOR ALL USING (auth.role() = 'service_role');`,
    `CREATE POLICY IF NOT EXISTS "Allow service role full access" ON github_issue_events FOR ALL USING (auth.role() = 'service_role');`
  ];

  try {
    // Execute SQL via REST API
    for (let i = 0; i < sqlStatements.length; i++) {
      const sql = sqlStatements[i];
      console.log(`Executing statement ${i + 1}/${sqlStatements.length}...`);
      
      const response = await fetch(`${supabaseUrl}/rest/v1/rpc/exec_sql`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${serviceKey}`,
          'apikey': serviceKey
        },
        body: JSON.stringify({ sql })
      });

      if (!response.ok) {
        const error = await response.text();
        console.log(`⚠️ Statement ${i + 1} warning: ${error}`);
      } else {
        console.log(`✅ Statement ${i + 1} executed`);
      }
    }

    console.log('');
    console.log('🎉 Database setup complete!');
    console.log('');
    console.log('📋 Manual verification:');
    console.log('Go to: https://supabase.com/dashboard/project/skovzcqcmwdzgelqhwuq/editor');
    console.log('You should see these tables:');
    console.log('• github_installations');
    console.log('• github_repositories');
    console.log('• github_push_events');
    console.log('• github_pull_request_events');
    console.log('• github_issue_events');
    console.log('');
    console.log('Next steps:');
    console.log('1. npm run dev');
    console.log('2. npm run test:complete');

  } catch (error) {
    console.error('❌ Failed to create tables:', error.message);
    
    console.log('');
    console.log('📋 Manual fallback - Copy this SQL to Supabase dashboard:');
    console.log('Go to: https://supabase.com/dashboard/project/skovzcqcmwdzgelqhwuq/sql');
    console.log('');
    console.log(sqlStatements.join('\n\n'));
  }
}

if (require.main === module) {
  createTablesDirect().catch(console.error);
}

module.exports = { createTablesDirect };
