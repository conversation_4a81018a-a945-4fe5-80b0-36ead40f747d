#!/usr/bin/env node

/**
 * Complete Supabase Setup Script
 * Handles database setup and migration application
 */

const fs = require('fs');
const path = require('path');

async function setupSupabase() {
  console.log('🚀 Supabase Setup for GitHub Webhooks');
  console.log('=====================================');
  console.log('');

  console.log('📋 Current Status:');
  console.log('✅ Project linked: skovzcqcmwdzgelqhwuq');
  console.log('✅ Environment variables configured');
  console.log('✅ Migration file ready');
  console.log('');

  console.log('🔧 To fix the CLI connection issue:');
  console.log('');
  console.log('1. 🔑 Reset your database password:');
  console.log('   Go to: https://supabase.com/dashboard/project/skovzcqcmwdzgelqhwuq/settings/database');
  console.log('   Click "Reset database password"');
  console.log('   Set a new password and remember it');
  console.log('');

  console.log('2. 📊 Apply the migration manually:');
  console.log('   Go to: https://supabase.com/dashboard/project/skovzcqcmwdzgelqhwuq/sql');
  console.log('   Click "New query"');
  console.log('   Copy and paste the SQL below:');
  console.log('');

  // Read and display the migration
  const migrationPath = path.join(__dirname, '../supabase/migrations/20241201000000_github_webhooks.sql');
  
  if (fs.existsSync(migrationPath)) {
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    console.log('=' .repeat(80));
    console.log('GITHUB WEBHOOK MIGRATION SQL:');
    console.log('=' .repeat(80));
    console.log(migrationSQL);
    console.log('=' .repeat(80));
  } else {
    console.log('❌ Migration file not found!');
  }

  console.log('');
  console.log('3. 🧪 Test the setup:');
  console.log('   After applying the SQL, run:');
  console.log('   npm run dev');
  console.log('   npm run test:github-webhook');
  console.log('');

  console.log('4. 🔐 Set up Doppler secrets:');
  console.log('   npm run setup:doppler');
  console.log('');

  console.log('5. 🎯 Create your GitHub App:');
  console.log('   Go to: https://github.com/settings/apps/new');
  console.log('   Webhook URL: https://yourdomain.com/api/github/webhooks');
  console.log('   Set permissions: Contents (read), Issues (read), Pull requests (read)');
  console.log('   Subscribe to events: installation, push, pull_request, issues');
  console.log('');

  console.log('✨ Once complete, you\'ll have:');
  console.log('   • Real-time GitHub webhook processing');
  console.log('   • Secure secret management with Doppler');
  console.log('   • Live dashboard at /github');
  console.log('   • Full CLI functionality');
  console.log('');
}

async function checkTables() {
  console.log('🔍 Checking if GitHub webhook tables exist...');
  console.log('');
  console.log('Run this query in your Supabase SQL editor to check:');
  console.log('');
  console.log('SELECT table_name FROM information_schema.tables');
  console.log('WHERE table_schema = \'public\'');
  console.log('AND table_name LIKE \'github_%\';');
  console.log('');
  console.log('Expected tables:');
  console.log('• github_installations');
  console.log('• github_repositories');
  console.log('• github_push_events');
  console.log('• github_pull_request_events');
  console.log('• github_issue_events');
}

async function testConnection() {
  console.log('🔌 Testing Supabase connection...');
  console.log('');
  console.log('Run this in your terminal after resetting the password:');
  console.log('npm run supabase:push');
  console.log('');
  console.log('If it works, you should see:');
  console.log('✅ Connecting to remote database...');
  console.log('✅ Finished supabase db push.');
}

// Main execution
if (require.main === module) {
  const command = process.argv[2];
  
  switch (command) {
    case 'check':
      checkTables();
      break;
    case 'test':
      testConnection();
      break;
    default:
      setupSupabase();
  }
}

module.exports = { setupSupabase, checkTables, testConnection };
