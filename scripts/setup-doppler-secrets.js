#!/usr/bin/env node

/**
 * Doppler Setup Script for GitHub Webhook Integration
 * Helps set up required secrets in Doppler
 */

const DopplerSDK = require('@dopplerhq/node-sdk');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const PROJECT = 'ai-sdlc';
const CONFIG_ENV = 'prd';

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

function questionSecret(prompt) {
  return new Promise((resolve) => {
    process.stdout.write(prompt);
    process.stdin.setRawMode(true);
    process.stdin.resume();
    process.stdin.setEncoding('utf8');
    
    let input = '';
    process.stdin.on('data', (char) => {
      if (char === '\u0003') { // Ctrl+C
        process.exit();
      } else if (char === '\r' || char === '\n') {
        process.stdin.setRawMode(false);
        process.stdin.pause();
        process.stdout.write('\n');
        resolve(input);
      } else if (char === '\u007f') { // Backspace
        if (input.length > 0) {
          input = input.slice(0, -1);
          process.stdout.write('\b \b');
        }
      } else {
        input += char;
        process.stdout.write('*');
      }
    });
  });
}

async function setupDopplerSecrets() {
  console.log('🔐 Doppler GitHub Webhook Setup');
  console.log('================================');
  console.log('');

  // Get Doppler token
  const dopplerToken = process.env.DOPPLER_TOKEN || await question('Enter your Doppler token: ');
  
  if (!dopplerToken || dopplerToken === 'dp.xx.yyy') {
    console.error('❌ Valid Doppler token is required');
    process.exit(1);
  }

  const doppler = new DopplerSDK({ accessToken: dopplerToken });

  console.log(`📡 Using Doppler project: ${PROJECT}, config: ${CONFIG_ENV}`);
  console.log('');

  // Test connection
  try {
    await doppler.secrets.list(PROJECT, CONFIG_ENV);
    console.log('✅ Doppler connection successful');
  } catch (error) {
    console.error('❌ Failed to connect to Doppler:', error.message);
    process.exit(1);
  }

  console.log('');
  console.log('🔧 Setting up GitHub App secrets...');
  console.log('');

  // GitHub App secrets
  const secrets = [
    {
      name: 'GITHUB_APP_ID',
      prompt: 'GitHub App ID: ',
      description: 'Your GitHub App ID (numeric)'
    },
    {
      name: 'GITHUB_CLIENT_ID',
      prompt: 'GitHub Client ID: ',
      description: 'Your GitHub App Client ID'
    },
    {
      name: 'GITHUB_CLIENT_SECRET',
      prompt: 'GitHub Client Secret: ',
      description: 'Your GitHub App Client Secret',
      secret: true
    },
    {
      name: 'GITHUB_PRIVATE_KEY',
      prompt: 'GitHub Private Key (base64): ',
      description: 'Your GitHub App Private Key (base64 encoded)',
      secret: true,
      multiline: true
    },
    {
      name: 'GITHUB_WEBHOOK_SECRET',
      prompt: 'GitHub Webhook Secret: ',
      description: 'Your GitHub App Webhook Secret',
      secret: true
    }
  ];

  for (const secretConfig of secrets) {
    console.log(`\n📝 ${secretConfig.description}`);
    
    let value;
    if (secretConfig.secret) {
      if (secretConfig.multiline) {
        console.log('Enter the base64 encoded private key (paste and press Enter):');
        value = await question('');
      } else {
        value = await questionSecret(secretConfig.prompt);
      }
    } else {
      value = await question(secretConfig.prompt);
    }

    if (value.trim()) {
      try {
        await doppler.secrets.update(PROJECT, CONFIG_ENV, secretConfig.name, value.trim());
        console.log(`✅ Set ${secretConfig.name}`);
      } catch (error) {
        console.error(`❌ Failed to set ${secretConfig.name}:`, error.message);
      }
    } else {
      console.log(`⏭️ Skipped ${secretConfig.name}`);
    }
  }

  console.log('');
  console.log('🔍 Optional: GitHub OAuth secrets (for user authentication)');
  
  const oauthSecrets = [
    {
      name: 'SUPABASE_AUTH_EXTERNAL_GITHUB_CLIENT_ID',
      prompt: 'GitHub OAuth Client ID: ',
      description: 'GitHub OAuth App Client ID (for user login)'
    },
    {
      name: 'SUPABASE_AUTH_EXTERNAL_GITHUB_SECRET',
      prompt: 'GitHub OAuth Secret: ',
      description: 'GitHub OAuth App Secret',
      secret: true
    },
    {
      name: 'SUPABASE_AUTH_EXTERNAL_GITHUB_REDIRECT_URI',
      prompt: 'GitHub OAuth Redirect URI: ',
      description: 'OAuth redirect URI',
      default: 'http://127.0.0.1:54321/auth/v1/callback'
    }
  ];

  const setupOAuth = await question('\nSet up GitHub OAuth secrets? (y/N): ');
  
  if (setupOAuth.toLowerCase() === 'y' || setupOAuth.toLowerCase() === 'yes') {
    for (const secretConfig of oauthSecrets) {
      console.log(`\n📝 ${secretConfig.description}`);
      
      let value;
      if (secretConfig.secret) {
        value = await questionSecret(secretConfig.prompt);
      } else {
        const prompt = secretConfig.default 
          ? `${secretConfig.prompt}(${secretConfig.default}): `
          : secretConfig.prompt;
        value = await question(prompt);
        
        if (!value.trim() && secretConfig.default) {
          value = secretConfig.default;
        }
      }

      if (value.trim()) {
        try {
          await doppler.secrets.update(PROJECT, CONFIG_ENV, secretConfig.name, value.trim());
          console.log(`✅ Set ${secretConfig.name}`);
        } catch (error) {
          console.error(`❌ Failed to set ${secretConfig.name}:`, error.message);
        }
      } else {
        console.log(`⏭️ Skipped ${secretConfig.name}`);
      }
    }
  }

  console.log('');
  console.log('🎉 Doppler setup complete!');
  console.log('');
  console.log('Next steps:');
  console.log('1. Create your GitHub App at: https://github.com/settings/apps/new');
  console.log('2. Set webhook URL to: https://yourdomain.com/api/github/webhooks');
  console.log('3. Install the app on your repositories');
  console.log('4. Test the webhook with: npm run test:github-webhook');
  console.log('');

  rl.close();
}

// Validation function
async function validateSecrets() {
  const dopplerToken = process.env.DOPPLER_TOKEN;
  
  if (!dopplerToken) {
    console.error('❌ DOPPLER_TOKEN environment variable is required');
    return false;
  }

  const doppler = new DopplerSDK({ accessToken: dopplerToken });

  try {
    const secrets = await doppler.secrets.list(PROJECT, CONFIG_ENV);
    
    const requiredSecrets = [
      'GITHUB_APP_ID',
      'GITHUB_CLIENT_ID', 
      'GITHUB_CLIENT_SECRET',
      'GITHUB_PRIVATE_KEY',
      'GITHUB_WEBHOOK_SECRET'
    ];

    const missing = [];
    
    for (const secret of requiredSecrets) {
      if (!secrets[secret]) {
        missing.push(secret);
      }
    }

    if (missing.length > 0) {
      console.log('❌ Missing required secrets:', missing.join(', '));
      return false;
    }

    console.log('✅ All required secrets are configured');
    return true;

  } catch (error) {
    console.error('❌ Failed to validate secrets:', error.message);
    return false;
  }
}

// Main execution
if (require.main === module) {
  const command = process.argv[2];
  
  if (command === 'validate') {
    validateSecrets().then(valid => {
      process.exit(valid ? 0 : 1);
    });
  } else {
    setupDopplerSecrets().catch(console.error);
  }
}

module.exports = { setupDopplerSecrets, validateSecrets };
