#!/bin/bash

# GitHub Webhook Test with curl
echo "🧪 Testing GitHub Webhook with curl"
echo "=================================="

WEBHOOK_URL="http://localhost:3001/api/github/webhooks"
WEBHOOK_SECRET="test-secret"

# Function to generate signature
generate_signature() {
    local payload="$1"
    local secret="$2"
    echo -n "$payload" | openssl dgst -sha256 -hmac "$secret" | sed 's/^.* //'
}

echo ""
echo "1. 🏥 Testing health check..."
curl -s -X GET "$WEBHOOK_URL" | jq '.' || echo "Health check response received"

echo ""
echo "2. 🏓 Testing ping webhook..."

PING_PAYLOAD='{
  "zen": "Non-blocking is better than blocking.",
  "hook_id": 12345678,
  "hook": {
    "type": "Repository",
    "id": 12345678,
    "name": "web",
    "active": true,
    "events": ["push", "pull_request"],
    "config": {
      "content_type": "json",
      "insecure_ssl": "0",
      "url": "'$WEBHOOK_URL'"
    }
  },
  "repository": {
    "id": 35129377,
    "name": "public-repo",
    "full_name": "baxterthehacker/public-repo",
    "owner": {
      "login": "baxterthehacker",
      "id": 6752317,
      "type": "User"
    },
    "private": false,
    "html_url": "https://github.com/baxterthehacker/public-repo",
    "description": "",
    "default_branch": "master"
  },
  "sender": {
    "login": "baxterthehacker",
    "id": 6752317,
    "type": "User"
  }
}'

SIGNATURE="sha256=$(generate_signature "$PING_PAYLOAD" "$WEBHOOK_SECRET")"

curl -s -X POST "$WEBHOOK_URL" \
  -H "Content-Type: application/json" \
  -H "X-GitHub-Event: ping" \
  -H "X-GitHub-Delivery: test-$(date +%s)" \
  -H "X-Hub-Signature-256: $SIGNATURE" \
  -H "User-Agent: GitHub-Hookshot/test" \
  -d "$PING_PAYLOAD" | jq '.' || echo "Ping webhook response received"

echo ""
echo "3. 📤 Testing push webhook..."

PUSH_PAYLOAD='{
  "ref": "refs/heads/main",
  "before": "95790bf891e76f994c616e1ea6c4e4b536b5a8b3",
  "after": "da1560886d4f094c3e6c9ef40349f7d38b5d27d7",
  "created": false,
  "deleted": false,
  "forced": false,
  "commits": [
    {
      "id": "da1560886d4f094c3e6c9ef40349f7d38b5d27d7",
      "message": "Update README.md",
      "timestamp": "2024-01-01T00:00:00Z",
      "author": {
        "name": "baxterthehacker",
        "email": "<EMAIL>"
      },
      "added": [],
      "removed": [],
      "modified": ["README.md"]
    }
  ],
  "repository": {
    "id": 35129377,
    "name": "public-repo",
    "full_name": "baxterthehacker/public-repo",
    "owner": {
      "login": "baxterthehacker",
      "id": 6752317,
      "type": "User"
    },
    "private": false,
    "default_branch": "master",
    "created_at": "2015-05-05T23:40:12Z",
    "updated_at": "2015-05-05T23:40:12Z",
    "pushed_at": "2015-05-05T23:40:27Z",
    "clone_url": "https://github.com/baxterthehacker/public-repo.git",
    "ssh_url": "**************:baxterthehacker/public-repo.git"
  },
  "pusher": {
    "name": "baxterthehacker",
    "email": "<EMAIL>"
  },
  "sender": {
    "login": "baxterthehacker",
    "id": 6752317,
    "type": "User"
  },
  "installation": {
    "id": 1
  }
}'

SIGNATURE="sha256=$(generate_signature "$PUSH_PAYLOAD" "$WEBHOOK_SECRET")"

curl -s -X POST "$WEBHOOK_URL" \
  -H "Content-Type: application/json" \
  -H "X-GitHub-Event: push" \
  -H "X-GitHub-Delivery: test-$(date +%s)" \
  -H "X-Hub-Signature-256: $SIGNATURE" \
  -H "User-Agent: GitHub-Hookshot/test" \
  -d "$PUSH_PAYLOAD" | jq '.' || echo "Push webhook response received"

echo ""
echo "🎉 Webhook tests completed!"
echo ""
echo "📊 Check the logs in your terminal running 'npm run dev'"
echo "🌐 Visit the dashboard: http://localhost:3001/github"
