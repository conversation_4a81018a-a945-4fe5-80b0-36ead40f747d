/**
 * GitHub Webhook Tables
 * Tables to store GitHub webhook events and installation data
 */

-- GitHub App Installations
create table github_installations (
  id bigserial primary key,
  installation_id bigint unique not null,
  account_id bigint not null,
  account_login text not null,
  account_type text not null check (account_type in ('User', 'Organization')),
  permissions jsonb not null default '{}',
  repository_selection text not null check (repository_selection in ('all', 'selected')),
  access_token text,
  expires_at timestamp with time zone,
  created_at timestamp with time zone not null,
  updated_at timestamp with time zone not null,
  inserted_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Indexes for github_installations
create index idx_github_installations_installation_id on github_installations(installation_id);
create index idx_github_installations_account_login on github_installations(account_login);
create index idx_github_installations_account_type on github_installations(account_type);

-- GitHub Repositories
create table github_repositories (
  id bigserial primary key,
  repository_id bigint unique not null,
  installation_id bigint references github_installations(installation_id) on delete cascade,
  name text not null,
  full_name text not null,
  private boolean not null default false,
  owner_login text not null,
  owner_type text not null,
  description text,
  default_branch text not null default 'main',
  html_url text not null,
  clone_url text not null,
  ssh_url text not null,
  created_at timestamp with time zone not null,
  updated_at timestamp with time zone not null,
  pushed_at timestamp with time zone,
  inserted_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Indexes for github_repositories
create index idx_github_repositories_repository_id on github_repositories(repository_id);
create index idx_github_repositories_installation_id on github_repositories(installation_id);
create index idx_github_repositories_full_name on github_repositories(full_name);
create index idx_github_repositories_owner_login on github_repositories(owner_login);

-- GitHub Push Events
create table github_push_events (
  id bigserial primary key,
  repository_id bigint not null,
  repository_name text not null,
  ref text not null,
  before_sha text not null,
  after_sha text not null,
  commit_count integer not null default 0,
  pusher_name text not null,
  pusher_email text not null,
  sender_login text not null,
  installation_id bigint,
  created_at timestamp with time zone not null,
  inserted_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Indexes for github_push_events
create index idx_github_push_events_repository_id on github_push_events(repository_id);
create index idx_github_push_events_repository_name on github_push_events(repository_name);
create index idx_github_push_events_ref on github_push_events(ref);
create index idx_github_push_events_sender_login on github_push_events(sender_login);
create index idx_github_push_events_created_at on github_push_events(created_at);

-- GitHub Pull Request Events
create table github_pull_request_events (
  id bigserial primary key,
  repository_id bigint not null,
  repository_name text not null,
  pull_request_id bigint not null,
  pull_request_number integer not null,
  action text not null,
  title text not null,
  state text not null,
  user_login text not null,
  head_ref text not null,
  base_ref text not null,
  sender_login text not null,
  installation_id bigint,
  created_at timestamp with time zone not null,
  inserted_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Indexes for github_pull_request_events
create index idx_github_pr_events_repository_id on github_pull_request_events(repository_id);
create index idx_github_pr_events_repository_name on github_pull_request_events(repository_name);
create index idx_github_pr_events_pr_number on github_pull_request_events(pull_request_number);
create index idx_github_pr_events_action on github_pull_request_events(action);
create index idx_github_pr_events_state on github_pull_request_events(state);
create index idx_github_pr_events_user_login on github_pull_request_events(user_login);
create index idx_github_pr_events_created_at on github_pull_request_events(created_at);

-- GitHub Issue Events
create table github_issue_events (
  id bigserial primary key,
  repository_id bigint not null,
  repository_name text not null,
  issue_id bigint not null,
  issue_number integer not null,
  action text not null,
  title text not null,
  state text not null,
  user_login text not null,
  labels text[] not null default '{}',
  sender_login text not null,
  installation_id bigint,
  created_at timestamp with time zone not null,
  inserted_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Indexes for github_issue_events
create index idx_github_issue_events_repository_id on github_issue_events(repository_id);
create index idx_github_issue_events_repository_name on github_issue_events(repository_name);
create index idx_github_issue_events_issue_number on github_issue_events(issue_number);
create index idx_github_issue_events_action on github_issue_events(action);
create index idx_github_issue_events_state on github_issue_events(state);
create index idx_github_issue_events_user_login on github_issue_events(user_login);
create index idx_github_issue_events_created_at on github_issue_events(created_at);

-- Row Level Security (RLS) policies
alter table github_installations enable row level security;
alter table github_repositories enable row level security;
alter table github_push_events enable row level security;
alter table github_pull_request_events enable row level security;
alter table github_issue_events enable row level security;

-- For now, allow authenticated users to read all GitHub data
-- In production, you might want more restrictive policies
create policy "Allow authenticated read access" on github_installations for select using (auth.role() = 'authenticated');
create policy "Allow authenticated read access" on github_repositories for select using (auth.role() = 'authenticated');
create policy "Allow authenticated read access" on github_push_events for select using (auth.role() = 'authenticated');
create policy "Allow authenticated read access" on github_pull_request_events for select using (auth.role() = 'authenticated');
create policy "Allow authenticated read access" on github_issue_events for select using (auth.role() = 'authenticated');

-- Allow service role to insert/update/delete (for webhook processing)
create policy "Allow service role full access" on github_installations for all using (auth.role() = 'service_role');
create policy "Allow service role full access" on github_repositories for all using (auth.role() = 'service_role');
create policy "Allow service role full access" on github_push_events for all using (auth.role() = 'service_role');
create policy "Allow service role full access" on github_pull_request_events for all using (auth.role() = 'service_role');
create policy "Allow service role full access" on github_issue_events for all using (auth.role() = 'service_role');

-- Add GitHub tables to realtime publication for real-time updates
alter publication supabase_realtime add table github_installations;
alter publication supabase_realtime add table github_repositories;
alter publication supabase_realtime add table github_push_events;
alter publication supabase_realtime add table github_pull_request_events;
alter publication supabase_realtime add table github_issue_events;
