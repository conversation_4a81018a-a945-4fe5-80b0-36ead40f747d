{"private": true, "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "prettier-fix": "prettier --write .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "stripe:login": "stripe login", "stripe:listen": "stripe listen --forward-to=localhost:3000/api/webhooks", "stripe:fixtures": "stripe fixtures fixtures/stripe-fixtures.json", "supabase:start": "npx supabase start", "supabase:stop": "npx supabase stop", "supabase:status": "npx supabase status", "supabase:restart": "npm run supabase:stop && npm run supabase:start", "supabase:reset": "npx supabase db reset", "supabase:link": "npx supabase link", "supabase:generate-types": "npx supabase gen types typescript --local --schema public > types_db.ts", "supabase:generate-migration": "npx supabase db diff | npx supabase migration new", "supabase:generate-seed": "npx supabase db dump --data-only -f supabase/seed.sql", "supabase:push": "npx supabase db push", "supabase:pull": "npx supabase db pull", "test:github-webhook": "node scripts/test-github-webhook.js", "setup:doppler": "node scripts/setup-doppler-secrets.js", "validate:doppler": "node scripts/setup-doppler-secrets.js validate", "migration:show": "node scripts/apply-migration.js", "setup:supabase": "node scripts/setup-supabase.js", "check:tables": "node scripts/setup-supabase.js check", "test:connection": "node scripts/setup-supabase.js test", "test:complete": "node scripts/test-complete-setup.js", "test:doppler": "node scripts/test-doppler-update.js"}, "dependencies": {"@dopplerhq/node-sdk": "^1.3.0", "@octokit/webhooks": "^14.0.2", "@octokit/webhooks-types": "^7.6.1", "@radix-ui/react-toast": "^1.1.5", "@stripe/stripe-js": "2.4.0", "@supabase/ssr": "^0.1.0", "@supabase/supabase-js": "^2.43.4", "@types/bcryptjs": "^2.4.6", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.0", "classnames": "^2.5.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "jsonwebtoken": "^9.0.2", "lucide-react": "0.330.0", "next": "14.2.3", "node-fetch": "^3.3.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^10.1.0", "react-merge-refs": "^2.1.1", "rehype-sanitize": "^6.0.0", "stripe": "^14.25.0", "tailwind-merge": "^2.3.0", "tailwindcss": "^3.4.4", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20.14.2", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-config-next": "14.1.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.34.2", "eslint-plugin-tailwindcss": "^3.17.3", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "postcss": "^8.4.38", "prettier": "^3.3.1", "prettier-plugin-tailwindcss": "^0.5.14", "supabase": "^2.24.3", "ts-jest": "^29.3.4", "typescript": "^5.4.5"}}