'use client';

import React, { useState, useEffect } from 'react';
import { useSpeechRecognition } from '@/hooks/useSpeechRecognition';

interface UserInputProps {
  onSendMessage: (message: string) => void;
  sendingMessage: boolean;
}

export default function UserInput({ onSendMessage, sendingMessage }: UserInputProps) {
  const [userInput, setUserInput] = useState('');
  
  const {
    isListening,
    transcript,
    startListening,
    stopListening,
    resetTranscript,
    hasRecognitionSupport
  } = useSpeechRecognition();

  // Update user input when speech recognition transcript changes
  useEffect(() => {
    if (isListening && transcript) {
      setUserInput(transcript);
    }
  }, [transcript, isListening]);

  // Reset transcript when stopping listening
  useEffect(() => {
    if (!isListening) {
      resetTranscript();
    }
  }, [isListening, resetTranscript]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (userInput.trim()) {
      onSendMessage(userInput);
      setUserInput('');
    }
  };

  const handleSpeechToggle = () => {
    if (isListening) {
      stopListening();
    } else {
      resetTranscript();
      startListening();
    }
  };

  return (
    <div
      style={{
        backgroundColor: 'var(--card-bg)',
        borderColor: 'var(--card-border)',
        color: 'var(--text-dark)'
      }}
      className="mb-4 border rounded-lg shadow-sm p-4"
    >
      <form onSubmit={handleSubmit} className="flex items-center space-x-2">
        <input
          type="text"
          value={userInput}
          onChange={(e) => setUserInput(e.target.value)}
          placeholder="Type your message here..."
          style={{
            backgroundColor: 'var(--input-bg)',
            borderColor: 'var(--input-border)',
            color: 'var(--input-text)'
          }}
          className="flex-1 p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          disabled={sendingMessage || isListening}
        />

        {/* Speech-to-text button */}
        {hasRecognitionSupport && (
          <button
            type="button"
            onClick={handleSpeechToggle}
            style={{
              backgroundColor: isListening ? 'var(--primary-dark)' : 'var(--primary-light)',
              color: isListening ? 'white' : 'var(--primary-dark)'
            }}
            className="p-2 rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2"
            title={isListening ? "Stop recording" : "Start speech-to-text"}
            disabled={sendingMessage}
          >
            {isListening ? (
              <span className="flex items-center">
                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 10a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z" />
                </svg>
              </span>
            ) : (
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
              </svg>
            )}
          </button>
        )}

        {/* Send button */}
        <button
          type="submit"
          style={{
            backgroundColor: 'var(--primary)',
            color: 'white'
          }}
          className={`px-4 py-2 rounded-md hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 ${
            sendingMessage || isListening || !userInput.trim() ? 'opacity-50 cursor-not-allowed' : ''
          }`}
          disabled={sendingMessage || isListening || !userInput.trim()}
        >
          {sendingMessage ? (
            <span className="flex items-center">
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Sending...
            </span>
          ) : (
            'Send'
          )}
        </button>
      </form>

      {/* Speech recognition status */}
      {isListening && (
        <div className="mt-2 text-sm flex items-center" style={{ color: 'var(--text-light)' }}>
          <span className="relative flex h-3 w-3 mr-2">
            <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
            <span className="relative inline-flex rounded-full h-3 w-3 bg-red-500"></span>
          </span>
          Recording... Speak now
        </div>
      )}
    </div>
  );
}
