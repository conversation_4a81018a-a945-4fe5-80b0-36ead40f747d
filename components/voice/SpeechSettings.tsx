'use client';

import React from 'react';

interface SpeechSettingsProps {
  voices: SpeechSynthesisVoice[];
  selectedVoice: SpeechSynthesisVoice | null;
  rate: number;
  autoSpeak: boolean;
  onVoiceChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  onRateChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onAutoSpeakChange: () => void;
}

export default function SpeechSettings({
  voices,
  selectedVoice,
  rate,
  autoSpeak,
  onVoiceChange,
  onRateChange,
  onAutoSpeakChange
}: SpeechSettingsProps) {
  return (
    <div style={{
      backgroundColor: 'var(--settings-bg)',
      borderColor: 'var(--settings-border)',
      color: 'var(--settings-text)'
    }} className="p-4 rounded-lg mb-4 shadow-sm border">
      <h2 className="text-lg font-semibold mb-2">Speech Settings</h2>
      <div className="flex flex-col md:flex-row gap-4">
        <div className="flex-1">
          <label className="block text-sm font-medium mb-1">
            Voice
          </label>
          <select
            style={{
              backgroundColor: 'var(--input-bg)',
              borderColor: 'var(--input-border)',
              color: 'var(--input-text)'
            }}
            className="w-full p-2 border rounded-md"
            onChange={onVoiceChange}
            value={voices.indexOf(selectedVoice as SpeechSynthesisVoice)}
            disabled={voices.length === 0}
          >
            <option value="-1">Default Voice</option>
            {voices.map((voice, index) => (
              <option key={voice.name} value={index}>
                {voice.name} ({voice.lang})
              </option>
            ))}
          </select>
          {voices.length === 0 && (
            <p className="text-xs text-amber-500 mt-1 font-medium">
              Loading voices... If no voices appear, your browser may not support speech synthesis.
            </p>
          )}
        </div>
        <div className="flex-1">
          <label className="block text-sm font-medium mb-1">
            Speed: {rate}x
          </label>
          <input
            type="range"
            min="0.5"
            max="2"
            step="0.1"
            value={rate}
            onChange={onRateChange}
            className="w-full accent-blue-500"
          />
        </div>
        <div className="flex items-end">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={autoSpeak}
              onChange={onAutoSpeakChange}
              className="mr-2 accent-blue-500 w-4 h-4"
            />
            <span className="text-sm font-medium">
              Auto-speak new messages
            </span>
          </label>
        </div>
      </div>
    </div>
  );
}
