'use client';

import React from 'react';

interface SpeechSettingsProps {
  voices: SpeechSynthesisVoice[];
  selectedVoice: SpeechSynthesisVoice | null;
  rate: number;
  autoSpeak: boolean;
  onVoiceChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  onRateChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onAutoSpeakChange: () => void;
}

export default function SpeechSettings({
  voices,
  selectedVoice,
  rate,
  autoSpeak,
  onVoiceChange,
  onRateChange,
  onAutoSpeakChange
}: SpeechSettingsProps) {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Speech Settings
        </h2>
      </div>

      {/* Voice Selection */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Voice
        </label>
        <select
          className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          onChange={onVoiceChange}
          value={voices.indexOf(selectedVoice as SpeechSynthesisVoice)}
          disabled={voices.length === 0}
        >
          <option value="-1">Default Voice</option>
          {voices.map((voice, index) => (
            <option key={voice.name} value={index}>
              {voice.name} ({voice.lang})
            </option>
          ))}
        </select>
        {voices.length === 0 && (
          <p className="text-xs text-amber-500 mt-2 font-medium">
            Loading voices... If no voices appear, your browser may not support speech synthesis.
          </p>
        )}
      </div>

      {/* Speed Control */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Speed: {rate}x
        </label>
        <input
          type="range"
          min="0.5"
          max="2"
          step="0.1"
          value={rate}
          onChange={onRateChange}
          className="w-full h-2 bg-gray-200 dark:bg-gray-600 rounded-lg appearance-none cursor-pointer accent-blue-500"
        />
        <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
          <span>0.5x</span>
          <span>1x</span>
          <span>2x</span>
        </div>
      </div>

      {/* Auto-speak Toggle */}
      <div>
        <label className="flex items-center space-x-3 cursor-pointer">
          <input
            type="checkbox"
            checked={autoSpeak}
            onChange={onAutoSpeakChange}
            className="w-5 h-5 text-blue-600 bg-gray-100 dark:bg-gray-600 border-gray-300 dark:border-gray-500 rounded focus:ring-blue-500 focus:ring-2"
          />
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Auto-speak new messages
          </span>
        </label>
      </div>
    </div>
  );
}
