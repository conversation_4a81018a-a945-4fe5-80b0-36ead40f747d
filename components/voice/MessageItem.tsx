'use client';

import React from 'react';
import ReactMarkdown from 'react-markdown';
import rehypeSanitize from 'rehype-sanitize';
import { type Message } from '@/types/message';

interface MessageItemProps {
  message: Message;
  onSpeak: (message: Message) => void;
  getTeammateInfo: (ai_teammate?: string) => any;
  formatTime: (timestamp: string) => string;
}

export default function MessageItem({
  message,
  onSpeak,
  getTeammateInfo,
  formatTime
}: MessageItemProps) {
  const teammateInfo = message.ai_teammate ? getTeammateInfo(message.ai_teammate) : null;
  const isUser = message.role === 'user';

  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}>
      <div className={`max-w-3xl ${isUser ? 'order-2' : 'order-1'}`}>
        {/* Message Header */}
        <div className={`flex items-center mb-2 ${isUser ? 'justify-end' : 'justify-start'}`}>
          {!isUser && teammateInfo && (
            <img
              src={teammateInfo.avatarUrl}
              alt={teammateInfo.fullName}
              className="w-8 h-8 rounded-full object-cover mr-3"
              onError={(e) => {
                (e.target as HTMLImageElement).src = 'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=150&h=150&fit=crop&crop=face';
              }}
            />
          )}

          <div className={`flex items-center space-x-2 ${isUser ? 'flex-row-reverse space-x-reverse' : ''}`}>
            <span className={`text-xs font-medium px-3 py-1 rounded-full ${
              isUser
                ? 'bg-blue-900 text-blue-200'
                : 'bg-gray-700 text-gray-200'
            }`}>
              {isUser ? 'You' : (teammateInfo?.fullName || 'Assistant')}
            </span>

            {!isUser && teammateInfo && (
              <span className="text-xs text-gray-400">
                {teammateInfo.jobTitle}
              </span>
            )}
          </div>
        </div>

        {/* Message Content */}
        <div className={`relative ${isUser ? 'ml-12' : 'mr-12'}`}>
          <div className={`rounded-2xl px-4 py-3 ${
            isUser
              ? 'bg-blue-600 text-white'
              : 'bg-gray-800 text-white border border-gray-700'
          } ${message.role === 'assistant' && !message.spoken ? 'ring-2 ring-blue-500 ring-opacity-50' : ''}`}>
            <div className={`prose prose-sm max-w-none ${isUser ? 'prose-invert' : ''}`}>
              <ReactMarkdown rehypePlugins={[rehypeSanitize]}>
                {message.text}
              </ReactMarkdown>
            </div>
          </div>

          {/* Message Footer */}
          <div className={`flex items-center mt-2 space-x-2 text-xs text-gray-400 ${
            isUser ? 'justify-end' : 'justify-start'
          }`}>
            <span>{formatTime(message.timestamp)}</span>

            {message.role === 'assistant' && message.spoken && (
              <span className="flex items-center text-green-400">
                <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                Spoken
              </span>
            )}

            {message.role === 'assistant' && teammateInfo && (
              <span className="text-blue-400">
                🎤 {teammateInfo.voice.split(' - ')[0]}
              </span>
            )}
          </div>

          {/* Speak Button */}
          {message.role === 'assistant' && (
            <button
              onClick={() => onSpeak(message)}
              className="absolute -right-12 top-2 p-2 text-gray-400 hover:text-blue-400 transition-colors rounded-full hover:bg-gray-700"
              title={message.spoken ? "Speak this message again" : "Speak this message"}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 14.142M9 9a3 3 0 000 6h3v-6H9z" />
              </svg>
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
