'use client';

import React from 'react';
import ReactMarkdown from 'react-markdown';
import rehypeSanitize from 'rehype-sanitize';
import { type Message } from '@/types/message';

interface MessageItemProps {
  message: Message;
  onSpeak: (message: Message) => void;
  getTeammateInfo: (ai_teammate?: string) => any;
  formatTime: (timestamp: string) => string;
}

export default function MessageItem({ 
  message, 
  onSpeak, 
  getTeammateInfo, 
  formatTime 
}: MessageItemProps) {
  const teammateInfo = message.ai_teammate ? getTeammateInfo(message.ai_teammate) : null;

  return (
    <li
      style={{
        borderColor: message.role === 'assistant'
          ? message.spoken ? 'var(--primary-dark)' : 'var(--primary)'
          : 'var(--primary)'
      }}
      className={`p-4 hover:opacity-90 ${message.role === 'assistant'
        ? message.spoken ? 'border-l-4 pl-3' : ''
        : 'border-r-4 pr-3'
        }`}
    >
      <div className="flex justify-between items-start">
        <div className="flex-1">
          <div className="flex items-center mb-2">
            {message.role === 'assistant' && teammateInfo ? (
              <div className="flex items-center space-x-2">
                <img
                  src={teammateInfo.avatarUrl}
                  alt={teammateInfo.fullName}
                  className="w-8 h-8 rounded-full object-cover"
                  onError={(e) => {
                    (e.target as HTMLImageElement).src = 'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=150&h=150&fit=crop&crop=face';
                  }}
                />
                <div>
                  <div className="flex items-center space-x-2">
                    <span
                      style={{
                        backgroundColor: 'var(--primary-light)',
                        color: 'var(--primary-dark)'
                      }}
                      className="text-xs font-medium px-2 py-1 rounded-full"
                    >
                      {teammateInfo.fullName}
                    </span>
                    <span
                      style={{ color: 'var(--text-light)' }}
                      className="text-xs"
                    >
                      {teammateInfo.jobTitle}
                    </span>
                  </div>
                </div>
              </div>
            ) : (
              <span
                style={{
                  backgroundColor: message.role === 'assistant' ? 'var(--primary-light)' : 'var(--primary-light)',
                  color: message.role === 'assistant' ? 'var(--primary-dark)' : 'var(--primary-dark)'
                }}
                className="text-xs font-medium px-2 py-1 rounded-full"
              >
                {message.role === 'assistant' 
                  ? (message.ai_teammate ? `Assistant (${message.ai_teammate})` : 'Assistant')
                  : 'You'
                }
              </span>
            )}
          </div>
          
          <div style={{ color: 'var(--text-dark)' }} className="font-medium prose prose-sm max-w-none">
            <ReactMarkdown rehypePlugins={[rehypeSanitize]}>
              {message.text}
            </ReactMarkdown>
          </div>
          
          <div className="flex items-center mt-1">
            <p style={{ color: 'var(--text-light)' }} className="text-sm">
              {formatTime(message.timestamp)}
            </p>
            {message.role === 'assistant' && message.spoken && (
              <span style={{ color: 'var(--primary-dark)' }} className="ml-2 text-xs flex items-center font-medium">
                <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                Spoken
              </span>
            )}
            {message.role === 'assistant' && teammateInfo && (
              <span style={{ color: 'var(--primary)' }} className="ml-2 text-xs flex items-center font-medium">
                🤖 Voice: {teammateInfo.voice}
              </span>
            )}
          </div>
        </div>
        {message.role === 'assistant' && (
          <button
            onClick={() => onSpeak(message)}
            style={{ color: 'var(--primary)' }}
            className="ml-2 p-2 rounded-full hover:opacity-80"
            title={message.spoken ? "Speak this message again" : "Speak this message"}
          >
            🔊
          </button>
        )}
      </div>
    </li>
  );
}
