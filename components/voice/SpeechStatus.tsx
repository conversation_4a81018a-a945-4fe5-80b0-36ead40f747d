'use client';

import React from 'react';

interface SpeechStatusProps {
  autoSpeak: boolean;
  isSpeaking: boolean;
  onCancel: () => void;
}

export default function SpeechStatus({ autoSpeak, isSpeaking, onCancel }: SpeechStatusProps) {
  return (
    <div className="space-y-4">
      {/* Status Indicator */}
      <div className="p-4 rounded-lg bg-gray-700 border border-gray-600">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${autoSpeak ? 'bg-green-500' : 'bg-gray-400'}`}></div>
            <span className="text-sm font-medium text-gray-300">
              Auto-speak {autoSpeak ? 'enabled' : 'disabled'}
            </span>
          </div>
        </div>
      </div>

      {/* Speaking Status */}
      {isSpeaking && (
        <div className="p-4 rounded-lg bg-blue-900/20 border border-blue-800">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="relative">
                <span className="animate-ping absolute inline-flex h-4 w-4 rounded-full bg-blue-400 opacity-75"></span>
                <span className="relative inline-flex rounded-full h-4 w-4 bg-blue-500"></span>
              </div>
              <span className="text-sm font-medium text-blue-300">
                Speaking...
              </span>
            </div>
            <button
              onClick={onCancel}
              className="px-3 py-1 bg-red-500 hover:bg-red-600 text-white text-xs rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
              title="Stop speaking"
            >
              Stop
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
