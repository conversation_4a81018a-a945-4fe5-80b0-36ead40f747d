'use client';

import React from 'react';

interface SpeechStatusProps {
  autoSpeak: boolean;
  isSpeaking: boolean;
  onCancel: () => void;
}

export default function SpeechStatus({ autoSpeak, isSpeaking, onCancel }: SpeechStatusProps) {
  return (
    <div
      style={{
        backgroundColor: 'var(--settings-bg)',
        borderColor: 'var(--settings-border)',
        color: 'var(--settings-text)'
      }}
      className="text-center p-2 rounded-lg border flex justify-center items-center space-x-4"
    >
      <p>
        {autoSpeak ? (
          <span style={{ color: 'var(--primary-dark)' }} className="font-medium">✓ Auto-speak is enabled</span>
        ) : (
          <span style={{ color: 'var(--text-dark)' }}>Auto-speak is disabled</span>
        )}
      </p>

      {isSpeaking && (
        <div className="flex items-center">
          <div className="flex items-center font-medium mr-3" style={{ color: 'var(--primary)' }}>
            <span className="relative flex h-3 w-3 mr-2">
              <span className="animate-ping absolute inline-flex h-full w-full rounded-full opacity-75" style={{ backgroundColor: 'var(--primary-light)' }}></span>
              <span className="relative inline-flex rounded-full h-3 w-3" style={{ backgroundColor: 'var(--primary)' }}></span>
            </span>
            Speaking...
          </div>
          <button
            onClick={onCancel}
            className="px-2 py-1 bg-red-500 text-white text-xs rounded-md hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
            title="Stop speaking"
          >
            Stop
          </button>
        </div>
      )}
    </div>
  );
}
