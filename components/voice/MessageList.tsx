'use client';

import React from 'react';
import SpeechSettings from './SpeechSettings';
import MessageDisplay from './MessageDisplay';
import UserInput from './UserInput';
import SpeechStatus from './SpeechStatus';
import { useMessageManager } from './hooks/useMessageManager';
import { useVoiceManager } from './hooks/useVoiceManager';
import { type Message } from '@/types/message';

export default function MessageList() {
  // Use custom hooks for state management
  const {
    messages,
    loading,
    sendingMessage,
    sendUserMessage,
    markMessageAsSpoken,
    handleAutoSpeak
  } = useMessageManager();

  const {
    selectedVoice,
    setSelectedVoice,
    rate,
    setRate,
    autoSpeak,
    setAutoSpeak,
    voices,
    isSpeaking,
    cancel,
    speakMessage,
    getTeammateInfo
  } = useVoiceManager();

  // Handle auto-speak when messages change
  React.useEffect(() => {
    handleAutoSpeak(speakMessage, autoSpeak, isSpeaking);
  }, [handleAutoSpeak, speakMessage, autoSpeak, isSpeaking]);

  // Event handlers
  const handleVoiceChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const voiceIndex = parseInt(e.target.value);
    setSelectedVoice(voices[voiceIndex] || null);
  };

  const handleRateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setRate(parseFloat(e.target.value));
  };

  const handleAutoSpeakChange = () => {
    setAutoSpeak(!autoSpeak);
  };

  const handleSpeakMessage = (message: Message) => {
    speakMessage(message, () => markMessageAsSpoken(message.id));
  };

  return (
    <div className="h-full w-full flex overflow-hidden">
      {/* Left Sidebar - Settings */}
      <div className="w-80 flex-shrink-0 bg-gray-800 border-r border-gray-700 p-6 overflow-y-auto">
        <SpeechSettings
          voices={voices}
          selectedVoice={selectedVoice}
          rate={rate}
          autoSpeak={autoSpeak}
          onVoiceChange={handleVoiceChange}
          onRateChange={handleRateChange}
          onAutoSpeakChange={handleAutoSpeakChange}
        />

        <div className="mt-8">
          <SpeechStatus
            autoSpeak={autoSpeak}
            isSpeaking={isSpeaking}
            onCancel={cancel}
          />
        </div>
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col bg-gray-900 overflow-hidden">
        {/* Messages */}
        <div className="flex-1 overflow-hidden">
          <MessageDisplay
            messages={messages}
            loading={loading}
            onSpeak={handleSpeakMessage}
            getTeammateInfo={getTeammateInfo}
          />
        </div>

        {/* Input Area */}
        <div className="flex-shrink-0 border-t border-gray-700 bg-gray-800 p-6">
          <UserInput
            onSendMessage={sendUserMessage}
            sendingMessage={sendingMessage}
          />
        </div>
      </div>
    </div>
  );
}


