'use client';

import React from 'react';
import SpeechSettings from './SpeechSettings';
import MessageDisplay from './MessageDisplay';
import UserInput from './UserInput';
import SpeechStatus from './SpeechStatus';
import { useMessageManager } from './hooks/useMessageManager';
import { useVoiceManager } from './hooks/useVoiceManager';
import { type Message } from '@/types/message';

export default function MessageList() {
  // Use custom hooks for state management
  const {
    messages,
    loading,
    sendingMessage,
    sendUserMessage,
    markMessageAsSpoken,
    handleAutoSpeak
  } = useMessageManager();

  const {
    selectedVoice,
    setSelectedVoice,
    rate,
    setRate,
    autoSpeak,
    setAutoSpeak,
    voices,
    isSpeaking,
    cancel,
    speakMessage,
    getTeammateInfo
  } = useVoiceManager();

  // Handle auto-speak when messages change
  React.useEffect(() => {
    handleAutoSpeak(speakMessage, autoSpeak, isSpeaking);
  }, [handleAutoSpeak, speakMessage, autoSpeak, isSpeaking]);

  // Event handlers
  const handleVoiceChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const voiceIndex = parseInt(e.target.value);
    setSelectedVoice(voices[voiceIndex] || null);
  };

  const handleRateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setRate(parseFloat(e.target.value));
  };

  const handleAutoSpeakChange = () => {
    setAutoSpeak(!autoSpeak);
  };

  const handleSpeakMessage = (message: Message) => {
    speakMessage(message, () => markMessageAsSpoken(message.id));
  };

  return (
    <div className="flex flex-col h-full">
      <SpeechSettings
        voices={voices}
        selectedVoice={selectedVoice}
        rate={rate}
        autoSpeak={autoSpeak}
        onVoiceChange={handleVoiceChange}
        onRateChange={handleRateChange}
        onAutoSpeakChange={handleAutoSpeakChange}
      />

      <MessageDisplay
        messages={messages}
        loading={loading}
        onSpeak={handleSpeakMessage}
        getTeammateInfo={getTeammateInfo}
      />

      <UserInput
        onSendMessage={sendUserMessage}
        sendingMessage={sendingMessage}
      />

      <SpeechStatus
        autoSpeak={autoSpeak}
        isSpeaking={isSpeaking}
        onCancel={cancel}
      />
    </div>
  );
}


