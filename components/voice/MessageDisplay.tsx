'use client';

import React, { useRef, useEffect } from 'react';
import MessageItem from './MessageItem';
import { type Message } from '@/types/message';

interface MessageDisplayProps {
  messages: Message[];
  loading: boolean;
  onSpeak: (message: Message) => void;
  getTeammateInfo: (ai_teammate?: string) => any;
}

export default function MessageDisplay({ 
  messages, 
  loading, 
  onSpeak, 
  getTeammateInfo 
}: MessageDisplayProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Format timestamp
  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  return (
    <div className="h-full flex flex-col">
      {loading ? (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mb-4"></div>
            <p className="text-gray-300 font-medium">Loading messages...</p>
          </div>
        </div>
      ) : messages.length === 0 ? (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center max-w-md mx-auto px-6">
            <div className="w-16 h-16 mx-auto mb-4 bg-blue-900 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">No messages yet</h3>
            <p className="text-gray-400">Start a conversation with your AI teammates. Messages will appear here and be spoken aloud.</p>
          </div>
        </div>
      ) : (
        <div className="flex-1 overflow-y-auto px-6 py-4">
          <div className="space-y-6 max-w-4xl mx-auto">
            {messages.slice(-50).map((message) => (
              <MessageItem
                key={message.id}
                message={message}
                onSpeak={onSpeak}
                getTeammateInfo={getTeammateInfo}
                formatTime={formatTime}
              />
            ))}
          </div>
          <div ref={messagesEndRef} />
        </div>
      )}
    </div>
  );
}
