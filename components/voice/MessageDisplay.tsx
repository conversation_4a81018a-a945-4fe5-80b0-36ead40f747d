'use client';

import React, { useRef, useEffect } from 'react';
import MessageItem from './MessageItem';
import { type Message } from '@/types/message';

interface MessageDisplayProps {
  messages: Message[];
  loading: boolean;
  onSpeak: (message: Message) => void;
  getTeammateInfo: (ai_teammate?: string) => any;
}

export default function MessageDisplay({ 
  messages, 
  loading, 
  onSpeak, 
  getTeammateInfo 
}: MessageDisplayProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Format timestamp
  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  return (
    <div
      style={{
        backgroundColor: 'var(--card-bg)',
        borderColor: 'var(--card-border)',
        color: 'var(--text-dark)'
      }}
      className="flex-1 overflow-y-auto mb-4 border rounded-lg shadow-sm"
    >
      {loading ? (
        <div className="p-8 text-center">
          <div
            className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 mb-2"
            style={{ borderColor: 'var(--primary)' }}
          ></div>
          <p style={{ color: 'var(--text-dark)' }} className="font-medium">Loading messages...</p>
        </div>
      ) : messages.length === 0 ? (
        <div className="p-8 text-center">
          <p style={{ color: 'var(--text-dark)' }} className="mb-2 font-medium text-lg">No messages yet</p>
          <p style={{ color: 'var(--text-light)' }}>When the MCP tool sends text, it will appear here and be spoken aloud.</p>
        </div>
      ) : (
        <ul style={{ borderColor: 'var(--card-border)' }} className="divide-y">
          {messages.slice(-20).map((message) => (
            <MessageItem
              key={message.id}
              message={message}
              onSpeak={onSpeak}
              getTeammateInfo={getTeammateInfo}
              formatTime={formatTime}
            />
          ))}
        </ul>
      )}
      <div ref={messagesEndRef} />
    </div>
  );
}
