'use client';

import { useState, useEffect, useCallback } from 'react';
import axios from 'axios';
import type { RealtimeChannel } from '@supabase/supabase-js';
import { type Message } from '@/types/message';
import { useAuth } from './useAuth';

export function useMessageManager() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const [sendingMessage, setSendingMessage] = useState(false);
  const [realtimeChannel, setRealtimeChannel] = useState<RealtimeChannel | null>(null);
  
  const { user, supabase, getAuthHeaders } = useAuth();

  // Fetch messages from the API - only get new messages from MCP tool
  const fetchMessages = useCallback(async () => {
    try {
      const headers = await getAuthHeaders();
      console.log('📨 Voice App - Fetching messages with auth:', !!headers.Authorization);

      const response = await axios.get('/api/mcp/speak', { headers });

      if (response.data && response.data.messages && response.data.messages.length > 0) {
        const newMessages = response.data.messages;
        console.log(`📨 Voice App - Received ${newMessages.length} new messages, authenticated: ${response.data.authenticated}`);
        setMessages(prev => [...prev, ...newMessages]);
        return newMessages;
      }
      return [];
    } catch (error) {
      console.error('Error fetching messages:', error);
      return [];
    } finally {
      setLoading(false);
    }
  }, [getAuthHeaders]);

  // Fetch all messages for initial load
  const fetchAllMessages = useCallback(async () => {
    try {
      setLoading(true);
      const headers = await getAuthHeaders();
      console.log('📨 Voice App - Fetching all messages with auth:', !!headers.Authorization);

      const response = await axios.get('/api/mcp/speak?all=true', { headers });

      if (response.data && response.data.messages) {
        console.log(`📨 Voice App - Loaded ${response.data.messages.length} messages, authenticated: ${response.data.authenticated}`);
        setMessages(response.data.messages);
      }
    } catch (error) {
      console.error('Error fetching all messages:', error);
    } finally {
      setLoading(false);
    }
  }, [getAuthHeaders]);

  // Load message history from simple_messages table
  const loadMessageHistory = useCallback(async () => {
    // Prevent multiple simultaneous loads
    if (loading) return;

    try {
      setLoading(true);
      console.log('📚 Loading message history from simple_messages...');

      const { data, error } = await supabase
        .from('simple_messages')
        .select('*')
        .order('created_at', { ascending: true })
        .limit(50);

      if (error) {
        console.error('Error loading history:', error);
        await fetchAllMessages();
        return;
      }

      if (data && data.length > 0) {
        const historyMessages: Message[] = data.map(msg => ({
          id: msg.id,
          text: msg.text,
          timestamp: msg.created_at || new Date().toISOString(),
          role: msg.role as 'user' | 'assistant',
          spoken: true,
          ai_teammate: msg.ai_teammate || undefined
        }));

        console.log(`📚 Loaded ${historyMessages.length} history messages`);
        setMessages(historyMessages);
      } else {
        console.log('📚 No history found, using existing system');
        await fetchAllMessages();
      }
    } catch (error) {
      console.error('Error loading history:', error);
      await fetchAllMessages();
    } finally {
      setLoading(false);
    }
  }, [supabase]); // Remove fetchAllMessages dependency to prevent re-creation

  // Send a user message
  const sendUserMessage = useCallback(async (userInput: string) => {
    if (!userInput.trim()) return;

    try {
      setSendingMessage(true);

      const tempMessage: Message = {
        id: 'temp-' + Date.now(),
        text: userInput,
        timestamp: new Date().toISOString(),
        role: 'user',
        spoken: true
      };

      setMessages(prev => [...prev, tempMessage]);

      const headers = await getAuthHeaders();
      console.log('📤 Voice App - Sending user message with auth:', !!headers.Authorization);

      try {
        const response = await axios.post('/api/user/speak', { text: userInput }, { headers });
        console.log('📤 Voice App - User message sent to existing API, authenticated:', response.data.authenticated);

        await axios.post('/api/simple/user', { text: userInput });
        console.log('📤 Voice App - User message sent to simple API');
      } catch (error) {
        console.error('Error sending to APIs:', error);
      }
    } catch (error) {
      console.error('Error sending user message:', error);
      setMessages(prev => prev.filter(msg => msg.id !== 'temp-' + Date.now()));
    } finally {
      setSendingMessage(false);
    }
  }, [getAuthHeaders]);

  // Mark message as spoken
  const markMessageAsSpoken = useCallback((messageId: string) => {
    setMessages(prevMessages =>
      prevMessages.map(msg =>
        msg.id === messageId ? { ...msg, spoken: true } : msg
      )
    );
  }, []);

  // Load history once on component mount
  useEffect(() => {
    loadMessageHistory();
  }, []); // Remove loadMessageHistory dependency to prevent re-runs

  // Set up real-time subscription for new messages
  useEffect(() => {
    console.log('🔔 Setting up real-time subscription...');

    const channel = supabase
      .channel('simple_messages_realtime')
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'simple_messages',
        filter: 'role=eq.assistant'
      }, (payload) => {
        console.log('🔔 Real-time: New assistant message', payload.new);

        const newMessage: Message = {
          id: payload.new.id,
          text: payload.new.text,
          timestamp: payload.new.created_at || new Date().toISOString(),
          role: payload.new.role as 'user' | 'assistant',
          spoken: false,
          ai_teammate: payload.new.ai_teammate || undefined
        };

        setMessages(prevMessages => {
          const exists = prevMessages.some(msg => msg.id === newMessage.id);
          if (exists) {
            console.log('🔔 Message already exists, skipping');
            return prevMessages;
          }

          console.log('🔔 Adding new message to UI');
          return [...prevMessages, newMessage];
        });
      })
      .subscribe();

    setRealtimeChannel(channel);

    return () => {
      console.log('🔔 Cleaning up real-time subscription');
      if (channel) {
        supabase.removeChannel(channel);
      }
    };
  }, [supabase]);

  return {
    messages,
    loading,
    sendingMessage,
    sendUserMessage,
    markMessageAsSpoken,
    fetchMessages
  };
}
