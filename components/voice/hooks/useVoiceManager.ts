'use client';

import { useState, useEffect, useCallback } from 'react';
import { useSpeech } from '@/hooks/useSpeech';
import { stripMarkdown } from '@/utils/textUtils';
import { AI_TEAMMATES, type Message, type AITeammateKey } from '@/types/message';

// Simple function to find voice by preference
function findVoiceByPreference(voices: SpeechSynthesisVoice[], voicePreference: string): SpeechSynthesisVoice | null {
  // Try exact match first
  const exactMatch = voices.find(v => v.name === voicePreference);
  if (exactMatch) return exactMatch;

  // Try partial matches
  if (voicePreference.includes('<PERSON>')) {
    return voices.find(v => v.name.includes('<PERSON>')) || null;
  }
  if (voicePreference.includes('David')) {
    return voices.find(v => v.name.includes('David')) || null;
  }
  if (voicePreference.includes('Google UK')) {
    return voices.find(v => v.name.includes('Google') && v.name.includes('UK')) || null;
  }

  return null;
}

export function useVoiceManager() {
  const [selectedVoice, setSelectedVoice] = useState<SpeechSynthesisVoice | null>(null);
  const [selectedMessage, setSelectedMessage] = useState<Message | null>(null);
  const [rate, setRate] = useState(1);
  const [autoSpeak, setAutoSpeak] = useState(true);
  const [voices, setVoices] = useState<SpeechSynthesisVoice[]>([]);
  
  const { speak, isSpeaking, cancel } = useSpeech();

  // Initialize voices
  useEffect(() => {
    const synth = window.speechSynthesis;

    const loadVoices = () => {
      const availableVoices = synth.getVoices();
      if (availableVoices.length > 0) {
        setVoices(availableVoices);
        console.log('Voices loaded:', availableVoices.length);
      }
    };

    loadVoices();

    if (typeof window !== 'undefined' && window.speechSynthesis) {
      window.speechSynthesis.onvoiceschanged = loadVoices;
    }

    return () => {
      if (typeof window !== 'undefined' && window.speechSynthesis) {
        window.speechSynthesis.onvoiceschanged = null;
      }
    };
  }, []);

  // Set up default voice
  useEffect(() => {
    if (voices.length > 0 && !selectedVoice) {
      const englishVoice = voices.find(voice => voice.lang.includes('en'));
      if (englishVoice) {
        setSelectedVoice(englishVoice);
      }
    }
  }, [voices, selectedVoice]);

  // Simple function to get teammate info
  const getTeammateInfo = (ai_teammate?: string) => {
    if (!ai_teammate) return null;
    const name = ai_teammate.toLowerCase() as AITeammateKey;
    return AI_TEAMMATES[name] || null;
  };

  // Simple function to detect AI teammate from message text
  const detectTeammate = (messageText: string): string | undefined => {
    if (!messageText.includes(':')) return undefined;

    const firstToken = String(messageText).split(":")[0].length > 15 
      ? String(messageText).split(":")[0].trim().toLowerCase() 
      : "system";

    if (AI_TEAMMATES[firstToken as AITeammateKey]) {
      return firstToken;
    }

    return undefined;
  };

  // Simple function to get voice for teammate
  const getVoiceForTeammate = (ai_teammate?: string): SpeechSynthesisVoice | null => {
    const teammateInfo = getTeammateInfo(ai_teammate);
    if (!teammateInfo) return selectedVoice;

    const voice = findVoiceByPreference(voices, teammateInfo.voice);
    return voice || selectedVoice;
  };

  // Speak a specific message
  const speakMessage = useCallback((message: Message, onEnd?: () => void) => {
    console.log("Speaking message:", message);
    if (isSpeaking) {
      cancel();
    }

    const plainText = stripMarkdown(message.text);

    // Extract ai_teammate from message text if not already set
    if (!message.ai_teammate) {
      message.ai_teammate = detectTeammate(plainText);
    }

    const name = (message.ai_teammate ?? "system").toLowerCase() as AITeammateKey;
    const voice = voices.find(v => v.name === AI_TEAMMATES[name]?.voice);
    
    speak(plainText, {
      voice: voice || selectedVoice,
      rate,
      onEnd
    });
  }, [voices, selectedVoice, rate, speak, isSpeaking, cancel]);

  return {
    selectedVoice,
    setSelectedVoice,
    rate,
    setRate,
    autoSpeak,
    setAutoSpeak,
    voices,
    isSpeaking,
    cancel,
    speakMessage,
    getTeammateInfo,
    detectTeammate,
    getVoiceForTeammate
  };
}
