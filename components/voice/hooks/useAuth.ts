'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@/utils/supabase/client';
import type { User } from '@supabase/supabase-js';

export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [supabase] = useState(() => createClient());

  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      setUser(user);
      console.log('🔐 Voice App - Current user:', user?.id || 'Anonymous');
    };

    getUser();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      setUser(session?.user || null);
      console.log('🔐 Voice App - Auth state changed:', session?.user?.id || 'Anonymous');
    });

    return () => subscription.unsubscribe();
  }, [supabase]);

  // Get authentication headers for API calls
  const getAuthHeaders = async () => {
    if (!user) return {};

    const { data: { session } } = await supabase.auth.getSession();
    if (session?.access_token) {
      return {
        'Authorization': `Bearer ${session.access_token}`
      };
    }
    return {};
  };

  return {
    user,
    supabase,
    getAuthHeaders
  };
}
