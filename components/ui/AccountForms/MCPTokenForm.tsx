'use client';

import { useState, useEffect } from 'react';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';

interface MCPTokenFormProps {
  userId: string;
}

export default function MCPTokenForm({ userId }: MCPTokenFormProps) {
  const [token, setToken] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [copied, setCopied] = useState(false);
  const [error, setError] = useState<string>('');
  const [success, setSuccess] = useState<string>('');
  const [existingKeys, setExistingKeys] = useState<any[]>([]);
  const [revokingKeys, setRevokingKeys] = useState<Set<string>>(new Set());

  const generateToken = async () => {
    setIsLoading(true);
    setError('');
    setSuccess('');

    try {
      const response = await fetch('/api/mcp/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate API key');
      }

      const data = await response.json();
      setToken(data.token);
      setSuccess('API key generated successfully! Copy it now - it won\'t be shown again.');

      // Refresh existing keys list
      loadExistingKeys();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to generate API key');
    } finally {
      setIsLoading(false);
    }
  };

  const loadExistingKeys = async () => {
    try {
      const response = await fetch('/api/mcp/token');
      if (response.ok) {
        const data = await response.json();
        setExistingKeys(data.apiKeys || []);
      }
    } catch (err) {
      console.error('Failed to load existing keys:', err);
    }
  };

  const revokeKey = async (keyId: string) => {
    setRevokingKeys(prev => new Set(prev).add(keyId));
    setError('');
    setSuccess('');

    try {
      const response = await fetch(`/api/mcp/token/${keyId}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to revoke API key');
      }

      setSuccess('API key revoked successfully');
      loadExistingKeys();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to revoke API key');
    } finally {
      setRevokingKeys(prev => {
        const newSet = new Set(prev);
        newSet.delete(keyId);
        return newSet;
      });
    }
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(token);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy token:', err);
    }
  };

  const clearToken = () => {
    setToken('');
    setCopied(false);
    setError('');
    setSuccess('');
  };

  // Load existing keys on component mount
  useEffect(() => {
    loadExistingKeys();
  }, []);

  return (
    <Card
      title="🔑 API Key Management"
      description="Generate API keys for external systems to access the AI-SDLC voice interface. Each key allows secure authentication for voice messaging and AI teammate interactions."
      footer={
        <div className="flex flex-col space-y-4">


          <div className="flex space-x-2">
            <Button
              variant="slim"
              type="button"
              onClick={generateToken}
              loading={isLoading}
              disabled={isLoading}
            >
              {token ? 'Generate New API Key' : 'Generate API Key'}
            </Button>
            
            {token && (
              <>
                <Button
                  variant="slim"
                  type="button"
                  onClick={copyToClipboard}
                  disabled={!token}
                >
                  {copied ? 'Copied!' : 'Copy Token'}
                </Button>
                
                <Button
                  variant="slim"
                  type="button"
                  onClick={clearToken}
                >
                  Clear
                </Button>
              </>
            )}
          </div>

          {error && (
            <div className="bg-red-900/20 border border-red-500/50 text-red-400 text-sm p-3 rounded">
              <strong>Error:</strong> {error}
            </div>
          )}

          {success && (
            <div className="bg-green-900/20 border border-green-500/50 text-green-400 text-sm p-3 rounded">
              <strong>Success:</strong> {success}
            </div>
          )}

          {token && (
            <div className="space-y-3 bg-zinc-900 p-4 rounded-lg border border-zinc-700">
              <div className="flex items-center justify-between">
                <div className="text-sm font-medium text-zinc-300">
                  🔑 New API Key Generated
                </div>
                <div className="text-xs text-amber-400 font-medium">
                  ⚠️ Copy now - won't be shown again!
                </div>
              </div>
              <div className="bg-zinc-800 p-3 rounded border text-sm font-mono break-all text-green-400">
                {token}
              </div>
              <div className="text-xs text-zinc-400 space-y-1">
                <p><strong>Usage:</strong> Include this key in your external system's Authorization header:</p>
                <code className="bg-zinc-800 px-2 py-1 rounded text-xs">Authorization: Bearer {token.substring(0, 20)}...</code>
                <p className="text-amber-400"><strong>Security:</strong> Keep this key secure and never share it publicly.</p>
              </div>
            </div>
          )}

          {!token && (
            <div className="bg-zinc-900 p-4 rounded-lg border border-zinc-700">
              <div className="text-sm text-zinc-300 space-y-3">
                <div>
                  <p className="font-medium text-zinc-200 mb-2">🚀 How to use API Keys:</p>
                  <ol className="list-decimal list-inside space-y-2 text-xs text-zinc-400">
                    <li><strong>Generate:</strong> Click "Generate API Key" to create a new authentication token</li>
                    <li><strong>Copy:</strong> Save the API key securely (it won't be shown again)</li>
                    <li><strong>Integrate:</strong> Use in your external system's Authorization header</li>
                    <li><strong>Manage:</strong> Revoke keys when no longer needed</li>
                  </ol>
                </div>

                <div className="border-t border-zinc-700 pt-3">
                  <p className="font-medium text-zinc-200 mb-2">📡 API Endpoints:</p>
                  <div className="space-y-1 text-xs text-zinc-400">
                    <p><code className="bg-zinc-800 px-1 rounded">POST /api/simple/user</code> - Send user messages</p>
                    <p><code className="bg-zinc-800 px-1 rounded">POST /api/simple/speak</code> - Send AI responses</p>
                    <p><code className="bg-zinc-800 px-1 rounded">GET /api/mcp/speak</code> - Get messages</p>
                  </div>
                </div>

                <div className="border-t border-zinc-700 pt-3">
                  <p className="font-medium text-zinc-200 mb-2">🔒 Security:</p>
                  <ul className="list-disc list-inside space-y-1 text-xs text-zinc-400">
                    <li>API keys never expire until manually revoked</li>
                    <li>Each key is unique and tied to your account</li>
                    <li>Messages are isolated per user/API key</li>
                    <li>Keep your API keys secure and private</li>
                  </ul>
                </div>
              </div>
            </div>
          )}

          {/* Existing API Keys */}
          {existingKeys.length > 0 && (
            <div className="space-y-3 border-t border-zinc-700 pt-6">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium text-zinc-300">🔐 Active API Keys</h4>
                <div className="text-xs text-zinc-500">
                  {existingKeys.length} key{existingKeys.length !== 1 ? 's' : ''} active
                </div>
              </div>
              <div className="space-y-2">
                {existingKeys.map((key) => (
                  <div key={key.id} className="flex items-center justify-between bg-zinc-800 p-4 rounded-lg border border-zinc-700 hover:border-zinc-600 transition-colors">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <div className="text-sm font-medium text-zinc-200">API Key</div>
                        <div className="w-2 h-2 bg-green-500 rounded-full" title="Active"></div>
                      </div>
                      <div className="text-xs text-zinc-400 mt-1">
                        Created: {new Date(key.createdAt).toLocaleDateString()} at {new Date(key.createdAt).toLocaleTimeString()}
                      </div>
                      <div className="text-xs text-zinc-500 mt-1">
                        ID: <code className="bg-zinc-700 px-1 rounded">{key.id.substring(0, 8)}...</code>
                      </div>
                    </div>
                    <Button
                      variant="slim"
                      type="button"
                      onClick={() => revokeKey(key.id)}
                      loading={revokingKeys.has(key.id)}
                      disabled={revokingKeys.has(key.id)}
                      className="ml-4 text-red-400 hover:text-red-300 hover:bg-red-900/20"
                    >
                      {revokingKeys.has(key.id) ? 'Revoking...' : 'Revoke'}
                    </Button>
                  </div>
                ))}
              </div>
              <div className="text-xs text-zinc-500 bg-zinc-900 p-3 rounded border border-zinc-700">
                <strong>Note:</strong> Revoking an API key will immediately disable access for any external systems using it.
              </div>
            </div>
          )}
        </div>
      }
    >
      <div className="space-y-4">
        <div className="bg-zinc-900 p-4 rounded-lg border border-zinc-700">
          <div className="text-sm text-zinc-300">
            <div className="flex items-center justify-between mb-3">
              <p className="font-medium text-zinc-200">👤 Account Information</p>
              <div className="text-xs text-zinc-500">Logged in</div>
            </div>
            <div className="space-y-2">
              <div>
                <span className="text-zinc-400">User ID:</span>
                <code className="ml-2 bg-zinc-800 px-2 py-1 rounded text-xs text-green-400">{userId}</code>
              </div>
              <div>
                <span className="text-zinc-400">Voice Interface:</span>
                <span className="ml-2 text-blue-400">✓ Accessible at /voice</span>
              </div>
            </div>
          </div>
        </div>

        <div className="text-sm text-zinc-400">
          <p>
            <strong className="text-zinc-300">🎯 Purpose:</strong> API keys enable external systems to securely access your AI-SDLC voice interface.
            Each key provides isolated access to voice messaging and AI teammate interactions for your applications.
          </p>
        </div>
      </div>
    </Card>
  );
}
