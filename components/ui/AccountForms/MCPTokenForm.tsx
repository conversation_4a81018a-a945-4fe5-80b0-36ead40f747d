'use client';

import { useState, useEffect } from 'react';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';

interface MCPTokenFormProps {
  userId: string;
}

export default function MCPTokenForm({ userId }: MCPTokenFormProps) {
  const [token, setToken] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [copied, setCopied] = useState(false);
  const [error, setError] = useState<string>('');
  const [existingKeys, setExistingKeys] = useState<any[]>([]);

  const generateToken = async () => {
    setIsLoading(true);
    setError('');

    try {
      const response = await fetch('/api/mcp/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error('Failed to generate API key');
      }

      const data = await response.json();
      setToken(data.token);

      // Refresh existing keys list
      loadExistingKeys();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to generate API key');
    } finally {
      setIsLoading(false);
    }
  };

  const loadExistingKeys = async () => {
    try {
      const response = await fetch('/api/mcp/token');
      if (response.ok) {
        const data = await response.json();
        setExistingKeys(data.apiKeys || []);
      }
    } catch (err) {
      console.error('Failed to load existing keys:', err);
    }
  };

  const revokeKey = async (keyId: string) => {
    try {
      const response = await fetch(`/api/mcp/token/${keyId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        loadExistingKeys();
      }
    } catch (err) {
      console.error('Failed to revoke key:', err);
    }
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(token);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy token:', err);
    }
  };

  const clearToken = () => {
    setToken('');
    setCopied(false);
    setError('');
  };

  // Load existing keys on component mount
  useEffect(() => {
    loadExistingKeys();
  }, []);

  return (
    <Card
      title="MCP Authentication Token"
      description="Generate a token for MCP client authentication. This token allows your MCP client to connect to the AI-SDLC voice interface."
      footer={
        <div className="flex flex-col space-y-4">


          <div className="flex space-x-2">
            <Button
              variant="slim"
              type="button"
              onClick={generateToken}
              loading={isLoading}
              disabled={isLoading}
            >
              {token ? 'Generate New API Key' : 'Generate API Key'}
            </Button>
            
            {token && (
              <>
                <Button
                  variant="slim"
                  type="button"
                  onClick={copyToClipboard}
                  disabled={!token}
                >
                  {copied ? 'Copied!' : 'Copy Token'}
                </Button>
                
                <Button
                  variant="slim"
                  type="button"
                  onClick={clearToken}
                >
                  Clear
                </Button>
              </>
            )}
          </div>

          {error && (
            <div className="text-red-500 text-sm">
              Error: {error}
            </div>
          )}

          {token && (
            <div className="space-y-2">
              <div className="text-sm text-zinc-400">
                API Key (never expires):
              </div>
              <div className="bg-zinc-800 p-3 rounded border text-sm font-mono break-all">
                {token}
              </div>
              <div className="text-xs text-zinc-500">
                Use this API key to configure your MCP client. Keep it secure and don't share it.
              </div>
            </div>
          )}

          {!token && (
            <div className="text-sm text-zinc-400">
              <p className="mb-2">
                <strong>How to use:</strong>
              </p>
              <ol className="list-decimal list-inside space-y-1 text-xs">
                <li>Click "Generate API Key" to create your authentication key</li>
                <li>Copy the API key and configure your MCP client</li>
                <li>Use the key to authenticate with the AI-SDLC voice interface</li>
                <li>API Keys never expire until revoked</li>
              </ol>
            </div>
          )}

          {/* Existing API Keys */}
          {existingKeys.length > 0 && (
            <div className="space-y-2 border-t border-zinc-700 pt-4">
              <h4 className="text-sm font-medium text-zinc-300">Existing API Keys:</h4>
              <div className="space-y-2">
                {existingKeys.map((key) => (
                  <div key={key.id} className="flex items-center justify-between bg-zinc-800 p-3 rounded border">
                    <div className="flex-1">
                      <div className="text-sm font-medium text-zinc-200">API Key</div>
                      <div className="text-xs text-zinc-400">
                        Created: {new Date(key.createdAt).toLocaleDateString()}
                      </div>
                    </div>
                    <Button
                      variant="slim"
                      type="button"
                      onClick={() => revokeKey(key.id)}
                      className="ml-2 text-red-400 hover:text-red-300"
                    >
                      Revoke
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      }
    >
      <div className="text-sm text-zinc-300">
        <p className="mb-2">
          <strong>User ID:</strong> <code className="bg-zinc-800 px-2 py-1 rounded text-xs">{userId}</code>
        </p>
        <p className="text-zinc-400">
          Generate an authentication token to connect your MCP client to the AI-SDLC voice interface. 
          This enables secure communication between your MCP tools and the voice messaging system.
        </p>
      </div>
    </Card>
  );
}
