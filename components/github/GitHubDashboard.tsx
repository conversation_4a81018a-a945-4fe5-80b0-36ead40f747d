'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@/utils/supabase/client';

interface Installation {
  id: number;
  installation_id: number;
  account_login: string;
  account_type: string;
  repository_selection: string;
  created_at: string;
}

interface PushEvent {
  id: number;
  repository_name: string;
  ref: string;
  commit_count: number;
  pusher_name: string;
  sender_login: string;
  created_at: string;
}

interface PREvent {
  id: number;
  repository_name: string;
  pull_request_number: number;
  action: string;
  title: string;
  state: string;
  user_login: string;
  created_at: string;
}

interface IssueEvent {
  id: number;
  repository_name: string;
  issue_number: number;
  action: string;
  title: string;
  state: string;
  user_login: string;
  labels: string[];
  created_at: string;
}

interface GitHubDashboardProps {
  installations: Installation[];
  pushEvents: PushEvent[];
  prEvents: PREvent[];
  issueEvents: IssueEvent[];
  errors: {
    installations: any;
    pushEvents: any;
    prEvents: any;
    issueEvents: any;
  };
}

export default function GitHubDashboard({
  installations: initialInstallations,
  pushEvents: initialPushEvents,
  prEvents: initialPREvents,
  issueEvents: initialIssueEvents,
  errors
}: GitHubDashboardProps) {
  const [installations, setInstallations] = useState(initialInstallations);
  const [pushEvents, setPushEvents] = useState(initialPushEvents);
  const [prEvents, setPREvents] = useState(initialPREvents);
  const [issueEvents, setIssueEvents] = useState(initialIssueEvents);
  const [isConnected, setIsConnected] = useState(false);

  const supabase = createClient();

  useEffect(() => {
    // Set up real-time subscriptions
    const installationsChannel = supabase
      .channel('github_installations')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'github_installations' },
        (payload) => {
          console.log('Installation change:', payload);
          if (payload.eventType === 'INSERT') {
            setInstallations(prev => [payload.new as Installation, ...prev]);
          } else if (payload.eventType === 'DELETE') {
            setInstallations(prev => prev.filter(i => i.id !== payload.old.id));
          }
        }
      )
      .subscribe();

    const pushEventsChannel = supabase
      .channel('github_push_events')
      .on('postgres_changes',
        { event: 'INSERT', schema: 'public', table: 'github_push_events' },
        (payload) => {
          console.log('New push event:', payload);
          setPushEvents(prev => [payload.new as PushEvent, ...prev.slice(0, 9)]);
        }
      )
      .subscribe();

    const prEventsChannel = supabase
      .channel('github_pull_request_events')
      .on('postgres_changes',
        { event: 'INSERT', schema: 'public', table: 'github_pull_request_events' },
        (payload) => {
          console.log('New PR event:', payload);
          setPREvents(prev => [payload.new as PREvent, ...prev.slice(0, 9)]);
        }
      )
      .subscribe();

    const issueEventsChannel = supabase
      .channel('github_issue_events')
      .on('postgres_changes',
        { event: 'INSERT', schema: 'public', table: 'github_issue_events' },
        (payload) => {
          console.log('New issue event:', payload);
          setIssueEvents(prev => [payload.new as IssueEvent, ...prev.slice(0, 9)]);
        }
      )
      .subscribe();

    // Check connection status
    const checkConnection = () => {
      setIsConnected(supabase.realtime.isConnected());
    };

    checkConnection();
    const interval = setInterval(checkConnection, 5000);

    return () => {
      clearInterval(interval);
      supabase.removeChannel(installationsChannel);
      supabase.removeChannel(pushEventsChannel);
      supabase.removeChannel(prEventsChannel);
      supabase.removeChannel(issueEventsChannel);
    };
  }, [supabase]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case 'opened': return 'text-green-400';
      case 'closed': return 'text-red-400';
      case 'merged': return 'text-purple-400';
      default: return 'text-blue-400';
    }
  };

  return (
    <div className="space-y-8">
      {/* Connection Status */}
      <div className="flex items-center space-x-2">
        <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-400' : 'bg-red-400'}`} />
        <span className="text-sm text-gray-400">
          Real-time connection: {isConnected ? 'Connected' : 'Disconnected'}
        </span>
      </div>

      {/* Installations */}
      <div className="bg-gray-900 rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">GitHub App Installations</h2>
        {errors.installations && (
          <div className="text-red-400 mb-4">
            Error loading installations: {errors.installations.message}
          </div>
        )}
        {installations.length === 0 ? (
          <p className="text-gray-400">No GitHub App installations found.</p>
        ) : (
          <div className="space-y-3">
            {installations.map((installation) => (
              <div key={installation.id} className="border border-gray-700 rounded p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-medium">{installation.account_login}</h3>
                    <p className="text-sm text-gray-400">
                      {installation.account_type} • {installation.repository_selection} repositories
                    </p>
                  </div>
                  <span className="text-xs text-gray-500">
                    {formatDate(installation.created_at)}
                  </span>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Recent Events Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Push Events */}
        <div className="bg-gray-900 rounded-lg p-6">
          <h2 className="text-lg font-semibold mb-4">Recent Pushes</h2>
          {errors.pushEvents && (
            <div className="text-red-400 mb-4 text-sm">
              Error: {errors.pushEvents.message}
            </div>
          )}
          <div className="space-y-3">
            {pushEvents.slice(0, 5).map((event) => (
              <div key={event.id} className="border-l-2 border-blue-500 pl-3">
                <div className="text-sm font-medium">{event.repository_name}</div>
                <div className="text-xs text-gray-400">
                  {event.commit_count} commit(s) to {event.ref.replace('refs/heads/', '')}
                </div>
                <div className="text-xs text-gray-500">
                  by {event.pusher_name} • {formatDate(event.created_at)}
                </div>
              </div>
            ))}
            {pushEvents.length === 0 && (
              <p className="text-gray-400 text-sm">No push events yet.</p>
            )}
          </div>
        </div>

        {/* Pull Request Events */}
        <div className="bg-gray-900 rounded-lg p-6">
          <h2 className="text-lg font-semibold mb-4">Pull Requests</h2>
          {errors.prEvents && (
            <div className="text-red-400 mb-4 text-sm">
              Error: {errors.prEvents.message}
            </div>
          )}
          <div className="space-y-3">
            {prEvents.slice(0, 5).map((event) => (
              <div key={event.id} className="border-l-2 border-green-500 pl-3">
                <div className="text-sm font-medium">
                  #{event.pull_request_number} {event.title}
                </div>
                <div className="text-xs text-gray-400">
                  {event.repository_name}
                </div>
                <div className="text-xs text-gray-500">
                  <span className={getActionColor(event.action)}>{event.action}</span>
                  {' '}by {event.user_login} • {formatDate(event.created_at)}
                </div>
              </div>
            ))}
            {prEvents.length === 0 && (
              <p className="text-gray-400 text-sm">No PR events yet.</p>
            )}
          </div>
        </div>

        {/* Issue Events */}
        <div className="bg-gray-900 rounded-lg p-6">
          <h2 className="text-lg font-semibold mb-4">Issues</h2>
          {errors.issueEvents && (
            <div className="text-red-400 mb-4 text-sm">
              Error: {errors.issueEvents.message}
            </div>
          )}
          <div className="space-y-3">
            {issueEvents.slice(0, 5).map((event) => (
              <div key={event.id} className="border-l-2 border-yellow-500 pl-3">
                <div className="text-sm font-medium">
                  #{event.issue_number} {event.title}
                </div>
                <div className="text-xs text-gray-400">
                  {event.repository_name}
                </div>
                <div className="text-xs text-gray-500">
                  <span className={getActionColor(event.action)}>{event.action}</span>
                  {' '}by {event.user_login} • {formatDate(event.created_at)}
                </div>
                {event.labels.length > 0 && (
                  <div className="flex flex-wrap gap-1 mt-1">
                    {event.labels.slice(0, 3).map((label, index) => (
                      <span key={index} className="text-xs bg-gray-700 px-1 rounded">
                        {label}
                      </span>
                    ))}
                  </div>
                )}
              </div>
            ))}
            {issueEvents.length === 0 && (
              <p className="text-gray-400 text-sm">No issue events yet.</p>
            )}
          </div>
        </div>
      </div>

      {/* Webhook Endpoint Info */}
      <div className="bg-gray-900 rounded-lg p-6">
        <h2 className="text-lg font-semibold mb-4">Webhook Configuration</h2>
        <div className="space-y-2 text-sm">
          <div>
            <span className="text-gray-400">Webhook URL:</span>
            <code className="ml-2 bg-gray-800 px-2 py-1 rounded">
              {typeof window !== 'undefined' ? `${window.location.origin}/api/github/webhooks` : '/api/github/webhooks'}
            </code>
          </div>
          <div>
            <span className="text-gray-400">Health Check:</span>
            <code className="ml-2 bg-gray-800 px-2 py-1 rounded">
              GET /api/github/webhooks
            </code>
          </div>
        </div>
      </div>
    </div>
  );
}
