// Dual-store message store: In-memory + Supabase with backward compatibility
// This maintains the original API while adding Supabase persistence

// Removed dual store - using database directly now
// Message interface for backward compatibility
export interface Message {
  id: string;
  text: string;
  timestamp: string;
  role: 'user' | 'assistant';
  sent: boolean;
  spoken?: boolean;
  ai_teammate?: string;
  session_id?: string;
  user_id?: string;
}

// In-memory storage for messages (kept for backward compatibility)
let messages: Message[] = [];

// Generate a unique ID for messages
function generateId(): string {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

// Helper to get user ID from request context (if available)
// This is a placeholder - in real implementation, you'd get this from auth context
function getCurrentUserId(): string | undefined {
  // For now, return undefined to maintain backward compatibility
  // In future, this would extract user ID from request context
  return undefined;
}

// Add a new message to the store (now uses dual-store)
export function addMessage(messageData: {
  text: string;
  role: 'user' | 'assistant';
  sent: boolean;
  ai_teammate?: string;
}, userIdOverride?: string): Message {
  const userId = userIdOverride || getCurrentUserId();

  // Use dual store for new functionality (it's async, but we'll handle it in background)
  const messageId = generateId();

  // Create message immediately for backward compatibility
  const message: Message = {
    id: messageId,
    text: messageData.text,
    timestamp: new Date().toISOString(),
    role: messageData.role,
    sent: messageData.sent,
    ai_teammate: messageData.ai_teammate
  };

  // Database storage is now handled directly in API routes
  // This function only maintains in-memory storage for backward compatibility
  messages.push(message);

  // Keep only the last 100 messages in memory
  if (messages.length > 100) {
    messages = messages.slice(-100);
  }

  return message;
}

// Get all messages (memory only - database access moved to API routes)
export async function getAllMessages(): Promise<Message[]> {
  // Database access is now handled directly in API routes
  // This function returns memory messages for backward compatibility
  return [...messages];
}

// Get messages by role
export function getMessagesByRole(role: 'user' | 'assistant'): Message[] {
  return messages.filter(msg => msg.role === role);
}

// Get unsent messages (memory only - database access moved to API routes)
export async function getUnsentMessages(role: 'user' | 'assistant', includeAll: boolean = false): Promise<Message[]> {
  // Database access is now handled directly in API routes
  // This function returns memory messages for backward compatibility
  if (includeAll) {
    return messages.filter(msg => msg.role === role);
  }
  return messages.filter(msg => msg.role === role && !msg.sent);
}

// Mark messages as sent (memory only - database doesn't track sent status)
export async function markMessagesAsSent(messagesToMark: Message[]): Promise<void> {
  const ids = messagesToMark.map(msg => msg.id);

  // Update memory for immediate consistency
  messages = messages.map(msg => {
    if (ids.includes(msg.id)) {
      return { ...msg, sent: true };
    }
    return msg;
  });
}

// Clear all messages (for testing) - memory only
export async function clearMessages(): Promise<void> {
  // Clear memory
  messages = [];
}

// Synchronous versions for backward compatibility (use memory only)
export function getAllMessagesSync(): Message[] {
  return [...messages];
}

export function getUnsentMessagesSync(role: 'user' | 'assistant', includeAll: boolean = false): Message[] {
  if (includeAll) {
    return messages.filter(msg => msg.role === role);
  }
  return messages.filter(msg => msg.role === role && !msg.sent);
}

export function markMessagesAsSentSync(messagesToMark: Message[]): void {
  const ids = messagesToMark.map(msg => msg.id);

  messages = messages.map(msg => {
    if (ids.includes(msg.id)) {
      return { ...msg, sent: true };
    }
    return msg;
  });
}

export function clearMessagesSync(): void {
  messages = [];
}
