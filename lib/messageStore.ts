// Dual-store message store: In-memory + Supabase with backward compatibility
// This maintains the original API while adding Supabase persistence

import dualStore from './dualMessageStore';
import type { LegacyMessage } from './voiceTypes';

// Use centralized type definition
export type Message = LegacyMessage;

// In-memory storage for messages (kept for backward compatibility)
let messages: Message[] = [];

// Generate a unique ID for messages
function generateId(): string {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

// Helper to get user ID from request context (if available)
// This is a placeholder - in real implementation, you'd get this from auth context
function getCurrentUserId(): string | undefined {
  // For now, return undefined to maintain backward compatibility
  // In future, this would extract user ID from request context
  return undefined;
}

// Add a new message to the store (now uses dual-store)
export function addMessage(messageData: {
  text: string;
  role: 'user' | 'assistant';
  sent: boolean;
  ai_teammate?: string;
}, userIdOverride?: string): Message {
  const userId = userIdOverride || getCurrentUserId();

  // Use dual store for new functionality (it's async, but we'll handle it in background)
  const messageId = generateId();

  // Create message immediately for backward compatibility
  const message: Message = {
    id: messageId,
    text: messageData.text,
    timestamp: new Date().toISOString(),
    role: messageData.role,
    sent: messageData.sent,
    ai_teammate: messageData.ai_teammate
  };

  // Add to dual store in background with the same ID (don't await to maintain sync API)
  dualStore.addMessage({
    text: messageData.text,
    role: messageData.role,
    sent: messageData.sent,
    ai_teammate: messageData.ai_teammate,
    id: messageId  // Pass the same ID to maintain consistency
  }, userId).catch(error => {
    console.error('Error adding message to dual store:', error);
  });

  // Also maintain in-memory storage for immediate backward compatibility
  messages.push(message);

  // Keep only the last 100 messages in memory
  if (messages.length > 100) {
    messages = messages.slice(-100);
  }

  return message;
}

// Get all messages (now uses dual-store with fallback)
export async function getAllMessages(): Promise<Message[]> {
  const userId = getCurrentUserId();

  try {
    const dualMessages = await dualStore.getAllMessages(userId);
    return dualMessages.map(msg => ({
      id: msg.id,
      text: msg.text,
      timestamp: msg.timestamp,
      role: msg.role,
      sent: msg.sent,
      ai_teammate: msg.ai_teammate
    }));
  } catch (error) {
    console.error('Error getting messages from dual store, falling back to memory:', error);
    return [...messages];
  }
}

// Get messages by role
export function getMessagesByRole(role: 'user' | 'assistant'): Message[] {
  return messages.filter(msg => msg.role === role);
}

// Get unsent messages (now uses dual-store with fallback)
export async function getUnsentMessages(role: 'user' | 'assistant', includeAll: boolean = false): Promise<Message[]> {
  const userId = getCurrentUserId();

  try {
    const dualMessages = await dualStore.getUnsentMessages(role, includeAll, userId);
    return dualMessages.map(msg => ({
      id: msg.id,
      text: msg.text,
      timestamp: msg.timestamp,
      role: msg.role,
      sent: msg.sent,
      ai_teammate: msg.ai_teammate
    }));
  } catch (error) {
    console.error('Error getting unsent messages from dual store, falling back to memory:', error);
    if (includeAll) {
      return messages.filter(msg => msg.role === role);
    }
    return messages.filter(msg => msg.role === role && !msg.sent);
  }
}

// Mark messages as sent (now uses dual-store)
export async function markMessagesAsSent(messagesToMark: Message[]): Promise<void> {
  const userId = getCurrentUserId();
  const ids = messagesToMark.map(msg => msg.id);

  try {
    // Update dual store
    await dualStore.markMessagesAsSent(messagesToMark.map(msg => ({
      id: msg.id,
      text: msg.text,
      timestamp: msg.timestamp,
      role: msg.role,
      sent: msg.sent,
      ai_teammate: msg.ai_teammate
    })), userId);
  } catch (error) {
    console.error('Error marking messages as sent in dual store:', error);
  }

  // Also update memory for immediate consistency
  messages = messages.map(msg => {
    if (ids.includes(msg.id)) {
      return { ...msg, sent: true };
    }
    return msg;
  });
}

// Clear all messages (for testing) - now uses dual-store
export async function clearMessages(): Promise<void> {
  const userId = getCurrentUserId();

  try {
    await dualStore.clearMessages(userId);
  } catch (error) {
    console.error('Error clearing messages in dual store:', error);
  }

  // Also clear memory
  messages = [];
}

// Synchronous versions for backward compatibility (use memory only)
export function getAllMessagesSync(): Message[] {
  return [...messages];
}

export function getUnsentMessagesSync(role: 'user' | 'assistant', includeAll: boolean = false): Message[] {
  if (includeAll) {
    return messages.filter(msg => msg.role === role);
  }
  return messages.filter(msg => msg.role === role && !msg.sent);
}

export function markMessagesAsSentSync(messagesToMark: Message[]): void {
  const ids = messagesToMark.map(msg => msg.id);

  messages = messages.map(msg => {
    if (ids.includes(msg.id)) {
      return { ...msg, sent: true };
    }
    return msg;
  });
}

export function clearMessagesSync(): void {
  messages = [];
}
