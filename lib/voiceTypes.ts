// Voice messaging types - centralized type definitions
// Uses generated Supabase types and provides convenient exports

import type { Tables, TablesInsert, TablesUpdate } from '@/types_db';

// Re-export Supabase types with cleaner names
export type VoiceMessage = Tables<'voice_messages'>;
export type VoiceMessageInsert = TablesInsert<'voice_messages'>;
export type VoiceMessageUpdate = TablesUpdate<'voice_messages'>;

export type VoiceSession = Tables<'voice_sessions'>;
export type VoiceSessionInsert = TablesInsert<'voice_sessions'>;
export type VoiceSessionUpdate = TablesUpdate<'voice_sessions'>;

// Legacy Message interface for backward compatibility
export interface LegacyMessage {
  id: string;
  text: string;
  timestamp: string;
  role: 'user' | 'assistant';
  sent: boolean;
  spoken?: boolean;
  ai_teammate?: string;
  session_id?: string;
  user_id?: string;
}

// AI Teammate types
export type AITeammate = 'sarah' | 'jordan' | 'alex' | 'system';

export interface AITeammateInfo {
  fullName: string;
  jobTitle: string;
  avatarUrl: string;
  voice: string;
}

// Message role type (stricter than database string)
export type MessageRole = 'user' | 'assistant';

// Voice configuration types
export interface VoiceConfig {
  useSupabase: boolean;
  fallbackToMemory: boolean;
  writeToMemory: boolean;
  readFromSupabase: boolean;
  enableRealTime: boolean;
  enableUserIsolation: boolean;
  enableSessionManagement: boolean;
  maxMessagesInMemory: number;
  sessionTimeoutMinutes: number;
  cleanupIntervalHours: number;
}

// API response types
export interface MessageResponse {
  success: boolean;
  message: string;
  messageId?: string;
  ai_teammate?: string;
}

export interface MessagesResponse {
  messages: LegacyMessage[];
}

// Helper type guards
export function isValidMessageRole(role: string): role is MessageRole {
  return role === 'user' || role === 'assistant';
}

export function isValidAITeammate(teammate: string): teammate is AITeammate {
  return ['sarah', 'jordan', 'alex', 'system'].includes(teammate);
}

// Type conversion helpers
export function voiceMessageToLegacy(voiceMsg: VoiceMessage): LegacyMessage {
  return {
    id: voiceMsg.id,
    text: voiceMsg.text,
    timestamp: voiceMsg.created_at || new Date().toISOString(),
    role: isValidMessageRole(voiceMsg.role) ? voiceMsg.role : 'assistant',
    sent: voiceMsg.sent || false,
    spoken: voiceMsg.spoken || false,
    ai_teammate: voiceMsg.ai_teammate || undefined,
    session_id: voiceMsg.session_id || undefined,
    user_id: voiceMsg.user_id || undefined
  };
}

export function legacyToVoiceMessageInsert(
  msg: Partial<LegacyMessage>, 
  userId: string, 
  sessionId: string
): VoiceMessageInsert {
  return {
    id: msg.id,
    text: msg.text!,
    role: msg.role!,
    ai_teammate: msg.ai_teammate || null,
    spoken: msg.spoken || false,
    sent: msg.sent || false,
    user_id: userId,
    session_id: sessionId
  };
}

// Error types
export class VoiceStoreError extends Error {
  constructor(
    message: string,
    public readonly operation: string,
    public readonly fallbackUsed: boolean = false
  ) {
    super(message);
    this.name = 'VoiceStoreError';
  }
}

export class VoiceSessionError extends Error {
  constructor(message: string, public readonly userId: string) {
    super(message);
    this.name = 'VoiceSessionError';
  }
}
