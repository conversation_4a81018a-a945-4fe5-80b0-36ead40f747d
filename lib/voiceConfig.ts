// Voice messaging configuration
// Controls dual-store behavior and feature flags

export interface VoiceConfig {
  // Dual-store configuration
  useSupabase: boolean;
  fallbackToMemory: boolean;
  writeToMemory: boolean;
  readFromSupabase: boolean;
  
  // Feature flags
  enableRealTime: boolean;
  enableUserIsolation: boolean;
  enableSessionManagement: boolean;
  
  // Performance settings
  maxMessagesInMemory: number;
  sessionTimeoutMinutes: number;
  cleanupIntervalHours: number;
}

// Default configuration - conservative approach for migration
const defaultConfig: VoiceConfig = {
  // Dual-store: Write to both, read from Supabase with memory fallback
  useSupabase: true,
  fallbackToMemory: true,
  writeToMemory: true,
  readFromSupabase: true,
  
  // Features: Gradually enable as we test
  enableRealTime: false,        // Start with polling
  enableUserIsolation: false,   // Start without user context
  enableSessionManagement: true, // Enable session tracking
  
  // Performance: Conservative limits
  maxMessagesInMemory: 100,
  sessionTimeoutMinutes: 60,
  cleanupIntervalHours: 24
};

// Environment-based configuration overrides
function getEnvironmentConfig(): Partial<VoiceConfig> {
  const env = process.env.NODE_ENV;
  
  switch (env) {
    case 'development':
      return {
        // More aggressive in development for testing
        enableUserIsolation: true,
        maxMessagesInMemory: 50
      };
      
    case 'production':
      return {
        // Conservative in production
        enableUserIsolation: false,
        fallbackToMemory: true
      };
      
    case 'test':
      return {
        // Memory-only for tests
        useSupabase: false,
        readFromSupabase: false,
        writeToMemory: true
      };
      
    default:
      return {};
  }
}

// Get final configuration
export function getVoiceConfig(): VoiceConfig {
  const envConfig = getEnvironmentConfig();
  return { ...defaultConfig, ...envConfig };
}

// Configuration presets for different migration phases
export const migrationPhases = {
  // Phase 1: Dual write, memory read (current)
  phase1: {
    useSupabase: true,
    writeToMemory: true,
    readFromSupabase: false,
    fallbackToMemory: true
  },
  
  // Phase 2: Dual write, Supabase read with fallback
  phase2: {
    useSupabase: true,
    writeToMemory: true,
    readFromSupabase: true,
    fallbackToMemory: true
  },
  
  // Phase 3: Supabase primary, memory cache
  phase3: {
    useSupabase: true,
    writeToMemory: true,
    readFromSupabase: true,
    fallbackToMemory: true,
    enableUserIsolation: true
  },
  
  // Phase 4: Supabase only
  phase4: {
    useSupabase: true,
    writeToMemory: false,
    readFromSupabase: true,
    fallbackToMemory: false,
    enableUserIsolation: true,
    enableRealTime: true
  }
};

// Apply migration phase configuration
export function setMigrationPhase(phase: keyof typeof migrationPhases): VoiceConfig {
  const baseConfig = getVoiceConfig();
  const phaseConfig = migrationPhases[phase];
  return { ...baseConfig, ...phaseConfig };
}

// Export singleton instance
let currentConfig: VoiceConfig = getVoiceConfig();

export function updateVoiceConfig(newConfig: Partial<VoiceConfig>): void {
  currentConfig = { ...currentConfig, ...newConfig };
}

export function getCurrentVoiceConfig(): VoiceConfig {
  return { ...currentConfig };
}
