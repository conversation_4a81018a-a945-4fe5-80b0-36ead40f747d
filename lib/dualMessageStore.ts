// Dual-store implementation: In-memory + Supabase
// Provides backward compatibility while migrating to persistent storage

import { createClient } from '@/utils/supabase/client';
import type {
  VoiceMessage,
  VoiceMessageInsert,
  VoiceMessageUpdate,
  VoiceSession,
  VoiceSessionInsert,
  VoiceSessionUpdate,
  LegacyMessage,
  VoiceConfig,
  VoiceStoreError,
  VoiceSessionError
} from './voiceTypes';

// Import helper functions as regular imports (not type imports)
import {
  voiceMessageToLegacy,
  legacyToVoiceMessageInsert
} from './voiceTypes';

// Re-export types for backward compatibility
export type Message = LegacyMessage;
export type { VoiceMessage, VoiceSession };

// Use VoiceConfig from types (subset for dual-store specific behavior)
type DualStoreConfig = Pick<VoiceConfig, 'useSupabase' | 'fallbackToMemory' | 'writeToMemory' | 'readFromSupabase'>;

class DualMessageStore {
  private config: DualStoreConfig;
  private memoryMessages: Message[] = [];
  private supabase = createClient();

  constructor(config: Partial<DualStoreConfig> = {}) {
    this.config = {
      useSupabase: false, // Temporarily disabled until migration
      fallbackToMemory: true,
      writeToMemory: true,
      readFromSupabase: false, // Temporarily disabled until migration
      ...config
    };
  }

  // Generate UUID-compatible ID
  private generateId(): string {
    return crypto.randomUUID();
  }

  // Sessions are no longer needed - simplified approach
  async getOrCreateSession(userId: string): Promise<string> {
    // Return user ID as session identifier (sessions removed)
    return userId || 'anonymous-session';
  }

  // Add message to both stores
  async addMessage(messageData: {
    text: string;
    role: 'user' | 'assistant';
    sent: boolean;
    spoken?: boolean;
    ai_teammate?: string;
    id?: string; // Optional ID for consistency with legacy API
  }, userId?: string): Promise<Message> {

    const message: Message = {
      id: messageData.id || this.generateId(),
      text: messageData.text,
      timestamp: new Date().toISOString(),
      role: messageData.role,
      sent: messageData.sent,
      spoken: messageData.spoken || false,
      ai_teammate: messageData.ai_teammate,
      user_id: userId
    };

    // Write to memory store (for backward compatibility)
    if (this.config.writeToMemory) {
      this.memoryMessages.push(message);
      // Keep only last 100 in memory
      if (this.memoryMessages.length > 100) {
        this.memoryMessages = this.memoryMessages.slice(-100);
      }
    }

    // Write to Supabase (simplified - using simple_messages)
    if (this.config.useSupabase && userId) {
      try {
        // Use simple_messages table instead
        const { error } = await this.supabase
          .from('simple_messages')
          .insert({
            text: message.text,
            role: message.role,
            ai_teammate: message.ai_teammate,
            user_id: userId // Will work after migration
          });

        if (error) throw error;
        console.log(`Message stored in Supabase - User: ${userId}`);

      } catch (error) {
        console.error('Error writing to Supabase:', error);
        if (!this.config.fallbackToMemory) {
          throw error;
        }
        // Continue with memory-only operation
      }
    }

    return message;
  }

  // Get all messages (memory only until migration)
  async getAllMessages(userId?: string): Promise<Message[]> {
    // Temporarily disabled Supabase until migration
    // TODO: Re-enable after migration with simple_messages table

    // Fallback to memory
    return [...this.memoryMessages];
  }

  // Get unsent messages
  async getUnsentMessages(role: 'user' | 'assistant', includeAll: boolean = false, userId?: string): Promise<Message[]> {
    if (this.config.readFromSupabase && this.config.useSupabase && userId) {
      try {
        let query = this.supabase
          .from('voice_messages')
          .select('*')
          .eq('user_id', userId)
          .eq('role', role)
          .order('created_at', { ascending: true });

        if (!includeAll) {
          query = query.eq('sent', false);
        }

        const { data, error } = await query;
        if (error) throw error;

        // Use helper function to convert to legacy format
        return data.map(voiceMessageToLegacy);

      } catch (error) {
        console.error('Error reading unsent messages from Supabase:', error);
        if (!this.config.fallbackToMemory) {
          throw error;
        }
      }
    }

    // Fallback to memory
    if (includeAll) {
      return this.memoryMessages.filter(msg => msg.role === role);
    }
    return this.memoryMessages.filter(msg => msg.role === role && !msg.sent);
  }

  // Mark messages as sent in both stores
  async markMessagesAsSent(messagesToMark: Message[], userId?: string): Promise<void> {
    const ids = messagesToMark.map(msg => msg.id);

    // Update memory store
    if (this.config.writeToMemory) {
      this.memoryMessages = this.memoryMessages.map(msg => {
        if (ids.includes(msg.id)) {
          return { ...msg, sent: true };
        }
        return msg;
      });
    }

    // Update Supabase using proper update type
    if (this.config.useSupabase && userId && ids.length > 0) {
      try {
        const updateData: VoiceMessageUpdate = { sent: true };

        const { error } = await this.supabase
          .from('voice_messages')
          .update(updateData)
          .in('id', ids)
          .eq('user_id', userId);

        if (error) throw error;

      } catch (error) {
        console.error('Error marking messages as sent in Supabase:', error);
        if (!this.config.fallbackToMemory) {
          throw error;
        }
      }
    }
  }

  // Mark messages as spoken
  async markMessagesAsSpoken(messageIds: string[], userId?: string): Promise<void> {
    // Update memory store
    if (this.config.writeToMemory) {
      this.memoryMessages = this.memoryMessages.map(msg => {
        if (messageIds.includes(msg.id)) {
          return { ...msg, spoken: true };
        }
        return msg;
      });
    }

    // Update Supabase using proper update type
    if (this.config.useSupabase && userId && messageIds.length > 0) {
      try {
        const updateData: VoiceMessageUpdate = { spoken: true };

        const { error } = await this.supabase
          .from('voice_messages')
          .update(updateData)
          .in('id', messageIds)
          .eq('user_id', userId);

        if (error) throw error;

      } catch (error) {
        console.error('Error marking messages as spoken in Supabase:', error);
        if (!this.config.fallbackToMemory) {
          throw error;
        }
      }
    }
  }

  // Clear messages (for testing)
  async clearMessages(userId?: string): Promise<void> {
    // Clear memory
    if (this.config.writeToMemory) {
      this.memoryMessages = [];
    }

    // Clear Supabase for user
    if (this.config.useSupabase && userId) {
      try {
        const { error } = await this.supabase
          .from('voice_messages')
          .delete()
          .eq('user_id', userId);

        if (error) throw error;

      } catch (error) {
        console.error('Error clearing messages in Supabase:', error);
        if (!this.config.fallbackToMemory) {
          throw error;
        }
      }
    }
  }

  // Configuration methods
  setConfig(newConfig: Partial<DualStoreConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  getConfig(): DualStoreConfig {
    return { ...this.config };
  }
}

// Create singleton instance
const dualStore = new DualMessageStore();

// Export both class and singleton for flexibility
export { DualMessageStore };
export default dualStore;
