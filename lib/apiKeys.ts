// Simple API Key management
// Just random strings mapped to users - that's it!

import { createClient } from '@/utils/supabase/server';

// Generate simple random API key
export function generateAPIKey(): string {
  // Just a simple random string - no prefixes, no complexity
  return Array.from(crypto.getRandomValues(new Uint8Array(32)))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');
}

// Create new API key for user with enhanced options
export async function createAPIKey(
  userId: string,
  options?: {
    name?: string;
    description?: string;
    rateLimitPerHour?: number;
    allowedOrigins?: string[];
    permissions?: Record<string, boolean>;
  }
): Promise<{ apiKey: string; keyId: string }> {
  const supabase = createClient();

  // Generate simple API key
  const apiKey = generateAPIKey();

  // Store in database with enhanced features
  const { data, error } = await supabase
    .from('api_keys')
    .insert({
      user_id: userId,
      api_key: api<PERSON>ey,
      active: true,
      name: options?.name || 'Unnamed API Key',
      description: options?.description || 'API key for external access',
      rate_limit_per_hour: options?.rateLimitPerHour || 1000,
      allowed_origins: options?.allowedOrigins || null,
      permissions: options?.permissions || { voice: true, messages: true }
    })
    .select('id')
    .single();

  if (error) {
    throw new Error(`Failed to create API key: ${error.message}`);
  }

  return { apiKey, keyId: data.id };
}

// Validate API key and return user info (enhanced after migration)
export async function validateAPIKey(apiKey: string): Promise<{
  userId: string;
  keyId: string;
  permissions: Record<string, boolean>;
} | null> {
  const supabase = createClient();

  try {
    // Simple lookup for now - will be enhanced after migration
    const { data, error } = await supabase
      .from('api_keys')
      .select('id, user_id')
      .eq('api_key', apiKey)
      .eq('active', true)
      .single();

    if (error || !data) {
      return null;
    }

    // TODO: After migration, add usage tracking and permission checks
    // For now, return basic info with default permissions
    return {
      userId: data.user_id!,
      keyId: data.id,
      permissions: { voice: true, messages: true }
    };
  } catch (error) {
    console.error('Error validating API key:', error);
    return null;
  }
}

// Get user's API keys
export async function getUserAPIKeys(userId: string): Promise<any[]> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('api_keys')
    .select('*')
    .eq('user_id', userId)
    .eq('active', true)
    .order('created_at', { ascending: false });

  if (error) {
    throw new Error(`Failed to get API keys: ${error.message}`);
  }

  return data || [];
}

// Revoke API key
export async function revokeAPIKey(keyId: string, userId: string): Promise<void> {
  const supabase = createClient();

  const { error } = await supabase
    .from('api_keys')
    .update({ active: false })
    .eq('id', keyId)
    .eq('user_id', userId);

  if (error) {
    throw new Error(`Failed to revoke API key: ${error.message}`);
  }
}

// Extract user ID from Authorization header - SIMPLE!
export async function getUserFromAuthHeader(authHeader: string | null): Promise<string | null> {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  const token = authHeader.slice(7); // Remove 'Bearer '

  // Try API key validation and extract user ID
  const result = await validateAPIKey(token);
  return result?.userId || null;
}

// Utility to get user from request
export async function getUserFromRequest(request: Request): Promise<string | null> {
  const authHeader = request.headers.get('Authorization');
  return await getUserFromAuthHeader(authHeader);
}
