// Simple API Key management
// Just random strings mapped to users - that's it!

import { createClient } from '@/utils/supabase/server';

// Generate simple random API key
export function generateAPIKey(): string {
  // Just a simple random string - no prefixes, no complexity
  return Array.from(crypto.getRandomValues(new Uint8Array(32)))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');
}

// Create new API key for user
export async function createAPIKey(userId: string): Promise<{ apiKey: string }> {
  const supabase = createClient();

  // Generate simple API key
  const apiKey = generateAPIKey();

  // Store in database - simple!
  const { error } = await supabase
    .from('api_keys')
    .insert({
      user_id: userId,
      api_key: apiKey,
      active: true
    });

  if (error) {
    throw new Error(`Failed to create API key: ${error.message}`);
  }

  return { apiKey };
}

// Validate API key and return user ID - SUPER SIMPLE!
export async function validateAPIKey(apiKey: string): Promise<string | null> {
  const supabase = createClient();

  try {
    // Simple lookup - no hashing, no prefixes, no complexity!
    const { data, error } = await supabase
      .from('api_keys')
      .select('user_id')
      .eq('api_key', apiKey)
      .eq('active', true)
      .single();

    if (error || !data) {
      return null;
    }

    return data.user_id;
  } catch (error) {
    console.error('Error validating API key:', error);
    return null;
  }
}

// Get user's API keys
export async function getUserAPIKeys(userId: string): Promise<any[]> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('api_keys')
    .select('*')
    .eq('user_id', userId)
    .eq('active', true)
    .order('created_at', { ascending: false });

  if (error) {
    throw new Error(`Failed to get API keys: ${error.message}`);
  }

  return data || [];
}

// Revoke API key
export async function revokeAPIKey(keyId: string, userId: string): Promise<void> {
  const supabase = createClient();

  const { error } = await supabase
    .from('api_keys')
    .update({ active: false })
    .eq('id', keyId)
    .eq('user_id', userId);

  if (error) {
    throw new Error(`Failed to revoke API key: ${error.message}`);
  }
}

// Extract user ID from Authorization header - SIMPLE!
export async function getUserFromAuthHeader(authHeader: string | null): Promise<string | null> {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  const token = authHeader.slice(7); // Remove 'Bearer '

  // Try API key validation (simple!)
  return await validateAPIKey(token);
}

// Utility to get user from request
export async function getUserFromRequest(request: Request): Promise<string | null> {
  const authHeader = request.headers.get('Authorization');
  return await getUserFromAuthHeader(authHeader);
}
