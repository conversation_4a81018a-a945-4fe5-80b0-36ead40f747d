import { handleInstallationDeleted } from '@/app/api/github/webhooks/handlers/installation-deleted';

const mockSupabaseDelete = jest.fn();
const mockSupabaseEq = jest.fn();
const mockSupabaseFrom = jest.fn();

jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => ({
    from: mockSupabaseFrom
  }))
}));

describe('handleInstallationDeleted', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    console.log = jest.fn();
    console.error = jest.fn();
    
    mockSupabaseEq.mockResolvedValue({ error: null });
    mockSupabaseDelete.mockReturnValue({ eq: mockSupabaseEq });
    mockSupabaseFrom.mockReturnValue({ delete: mockSupabaseDelete });
  });

  const mockEvent = {
    payload: {
      installation: {
        id: 12345,
        account: { id: 67890, login: 'test-org', type: 'Organization' }
      }
    }
  };

  it('should process installation deleted event', async () => {
    await handleInstallationDeleted(mockEvent);
    expect(mockSupabaseFrom).toHaveBeenCalledWith('github_installations');
    expect(mockSupabaseDelete).toHaveBeenCalled();
    expect(mockSupabaseEq).toHaveBeenCalledWith('installation_id', 12345);
  });

  it('should handle Supabase deletion errors', async () => {
    mockSupabaseEq.mockResolvedValue({ error: { message: 'DB error' } });
    await expect(handleInstallationDeleted(mockEvent)).rejects.toThrow('Supabase deletion failed: DB error');
  });

  it('should handle User account type', async () => {
    const event = { ...mockEvent };
    event.payload.installation.account.type = 'User';
    await handleInstallationDeleted(event);
    expect(mockSupabaseEq).toHaveBeenCalledWith('installation_id', 12345);
  });

  it('should handle network errors', async () => {
    const networkError = new Error('Network unreachable');
    mockSupabaseEq.mockRejectedValue(networkError);
    await expect(handleInstallationDeleted(mockEvent)).rejects.toThrow('Network unreachable');
  });

  it('should handle large installation IDs', async () => {
    const event = { ...mockEvent };
    event.payload.installation.id = ************;
    await handleInstallationDeleted(event);
    expect(mockSupabaseEq).toHaveBeenCalledWith('installation_id', ************);
  });

  it('should log processing messages', async () => {
    await handleInstallationDeleted(mockEvent);
    expect(console.log).toHaveBeenCalledWith('🗑️ Installation deleted', expect.any(Object));
    expect(console.log).toHaveBeenCalledWith('📊 Deleting installation from Supabase', expect.any(Object));
    expect(console.log).toHaveBeenCalledWith('✅ Installation deleted successfully', expect.any(Object));
  });

  it('should handle concurrent deletion requests', async () => {
    const promises = [
      handleInstallationDeleted(mockEvent),
      handleInstallationDeleted(mockEvent),
      handleInstallationDeleted(mockEvent)
    ];
    await Promise.all(promises);
    expect(mockSupabaseFrom).toHaveBeenCalledTimes(3);
    expect(mockSupabaseEq).toHaveBeenCalledTimes(3);
  });

  it('should handle error logging correctly', async () => {
    mockSupabaseEq.mockResolvedValue({ error: { message: 'DB connection failed' } });
    try {
      await handleInstallationDeleted(mockEvent);
    } catch (error) {
      // Expected
    }
    expect(console.error).toHaveBeenCalledWith('❌ Failed to process installation deletion', expect.any(Object));
  });
});
