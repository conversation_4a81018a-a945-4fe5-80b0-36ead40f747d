import { handleInstallationCreated } from '@/app/api/github/webhooks/handlers/installation-created';

const mockSupabaseInsert = jest.fn();
const mockSupabaseSelect = jest.fn();
const mockSupabaseFrom = jest.fn();

jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => ({
    from: mockSupabaseFrom
  }))
}));

describe('handleInstallationCreated', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    console.log = jest.fn();
    console.error = jest.fn();
    
    const mockSingle = jest.fn().mockResolvedValue({
      data: { id: 1, installation_id: 12345 },
      error: null
    });
    
    mockSupabaseSelect.mockReturnValue({ single: mockSingle });
    mockSupabaseInsert.mockReturnValue({ select: mockSupabaseSelect });
    mockSupabaseFrom.mockReturnValue({ insert: mockSupabaseInsert });
  });

  const mockEvent = {
    payload: {
      installation: {
        id: 12345,
        account: { id: 67890, login: 'test-org', type: 'Organization' },
        permissions: { issues: 'write', contents: 'read' },
        repository_selection: 'all',
        created_at: '2025-01-07T12:00:00Z'
      }
    }
  };

  it('should process installation created event', async () => {
    await handleInstallationCreated(mockEvent);
    expect(mockSupabaseFrom).toHaveBeenCalledWith('github_installations');
    expect(mockSupabaseInsert).toHaveBeenCalledWith({
      installation_id: 12345,
      account_id: 67890,
      account_login: 'test-org',
      account_type: 'Organization',
      permissions: { issues: 'write', contents: 'read' },
      repository_selection: 'all',
      created_at: '2025-01-07T12:00:00Z',
      updated_at: expect.any(String)
    });
  });

  it('should handle Supabase errors', async () => {
    mockSupabaseSelect.mockReturnValue({
      single: jest.fn().mockResolvedValue({
        data: null,
        error: { message: 'DB error' }
      })
    });
    await expect(handleInstallationCreated(mockEvent)).rejects.toThrow('Supabase insertion failed: DB error');
  });

  it('should handle missing permissions', async () => {
    const event = { ...mockEvent };
    event.payload.installation.permissions = undefined;
    await handleInstallationCreated(event);
    expect(mockSupabaseInsert).toHaveBeenCalledWith(expect.objectContaining({
      permissions: {}
    }));
  });

  it('should handle User account type', async () => {
    const event = { ...mockEvent };
    event.payload.installation.account.type = 'User';
    await handleInstallationCreated(event);
    expect(mockSupabaseInsert).toHaveBeenCalledWith(expect.objectContaining({
      account_type: 'User'
    }));
  });

  it('should handle network timeouts', async () => {
    const timeoutError = new Error('Timeout');
    timeoutError.name = 'TimeoutError';
    mockSupabaseSelect.mockReturnValue({
      single: jest.fn().mockRejectedValue(timeoutError)
    });
    await expect(handleInstallationCreated(mockEvent)).rejects.toThrow('Timeout');
  });

  it('should handle large installation IDs', async () => {
    const event = { ...mockEvent };
    event.payload.installation.id = ************;
    event.payload.installation.account.id = ************;
    await handleInstallationCreated(event);
    expect(mockSupabaseInsert).toHaveBeenCalledWith(expect.objectContaining({
      installation_id: ************,
      account_id: ************
    }));
  });

  it('should log processing messages', async () => {
    await handleInstallationCreated(mockEvent);
    expect(console.log).toHaveBeenCalledWith('🔧 New installation created', expect.any(Object));
    expect(console.log).toHaveBeenCalledWith('📊 Storing installation in Supabase', expect.any(Object));
    expect(console.log).toHaveBeenCalledWith('✅ Installation stored successfully', expect.any(Object));
  });

  it('should handle concurrent requests', async () => {
    const promises = [
      handleInstallationCreated(mockEvent),
      handleInstallationCreated(mockEvent),
      handleInstallationCreated(mockEvent)
    ];
    await Promise.all(promises);
    expect(mockSupabaseFrom).toHaveBeenCalledTimes(3);
  });

  it('should handle special characters in account login', async () => {
    const event = { ...mockEvent };
    event.payload.installation.account.login = 'test-org_123-special';
    await handleInstallationCreated(event);
    expect(mockSupabaseInsert).toHaveBeenCalledWith(expect.objectContaining({
      account_login: 'test-org_123-special'
    }));
  });

  it('should handle empty repository selection', async () => {
    const event = { ...mockEvent };
    event.payload.installation.repository_selection = undefined;
    await handleInstallationCreated(event);
    expect(mockSupabaseInsert).toHaveBeenCalledWith(expect.objectContaining({
      repository_selection: 'all'
    }));
  });
});
