import { NextResponse } from 'next/server';
import { addMessage, getUnsentMessages, markMessagesAsSent, getUnsentMessagesSync, markMessagesAsSentSync } from '@/lib/messageStore';
import { getUserFromRequest } from '@/lib/apiKeys';

// Route for web app to send user messages
export async function POST(request: Request) {
  try {
    // Extract user from authentication (API key, JWT, or session)
    const userId = await getUserFromRequest(request);

    const body = await request.json();
    const { text } = body;

    if (!text) {
      return NextResponse.json(
        { error: 'Text is required' },
        { status: 400 }
      );
    }

    // Add message from user (type: 'user') with user context
    const message = addMessage({
      text,
      role: 'user',
      sent: false
    }, userId || undefined);

    return NextResponse.json({
      success: true,
      message: 'User message received',
      messageId: message.id,
      authenticated: !!userId
    });
  } catch (error) {
    console.error('Error processing user speak request:', error);
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    );
  }
}

// Route for web app to get all user messages (for display purposes)
export async function GET(request: Request) {
  try {
    // Get the URL parameters
    const { searchParams } = new URL(request.url);
    const getAll = searchParams.get('all') === 'true';

    // Get user messages (role: 'user') - use async version for better consistency
    try {
      const userMessages = await getUnsentMessages('user', getAll);

      // If not getting all, mark these messages as sent
      if (!getAll) {
        await markMessagesAsSent(userMessages);
      }

      // Return the user messages
      return NextResponse.json({ messages: userMessages });
    } catch (error) {
      console.error('Error with async user message operations, falling back to sync:', error);
      const userMessages = getUnsentMessagesSync('user', getAll);

      if (!getAll) {
        markMessagesAsSentSync(userMessages);
      }

      return NextResponse.json({ messages: userMessages });
    }

  } catch (error) {
    console.error('Error in GET /api/user/speak:', error);
    return NextResponse.json(
      { error: 'Failed to get user messages' },
      { status: 500 }
    );
  }
}
