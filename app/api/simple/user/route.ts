import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { getUser } from '@/utils/supabase/queries';
import { getUserFromRequest } from '@/lib/apiKeys';

// Simple route for voice app to insert user messages
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { text } = body;

    if (!text) {
      return NextResponse.json(
        { error: 'Text is required' },
        { status: 400 }
      );
    }

    const supabase = createClient();

    // Get user from session (logged-in user) or API key (external user)
    let userId: string | null = null;

    try {
      // Try to get logged-in user first
      const user = await getUser(supabase);
      if (user) {
        userId = user.id;
        console.log('📝 User message from logged-in user:', userId);
      } else {
        // Try API key authentication
        userId = await getUserFromRequest(request);
        if (userId) {
          console.log('📝 User message from API key user:', userId);
        }
      }
    } catch (error) {
      console.log('📝 No authentication found, allowing anonymous message');
    }

    // Insert message with user_id (will be null for anonymous messages)
    const { data, error } = await supabase
      .from('simple_messages')
      .insert({
        text: text,
        role: 'user',
        ai_teammate: null,
        user_id: userId // Will be added by migration
      })
      .select()
      .single();

    if (error) {
      console.error('Error inserting simple user message:', error);
      return NextResponse.json(
        { error: 'Failed to insert message' },
        { status: 500 }
      );
    }

    console.log(`✅ Simple user message inserted`);

    return NextResponse.json({
      success: true,
      message: 'User message inserted successfully',
      messageId: data.id
    });
  } catch (error) {
    console.error('Error in simple user route:', error);
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    );
  }
}
