import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

// Simple route for voice app to insert user messages
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { text } = body;

    if (!text) {
      return NextResponse.json(
        { error: 'Text is required' },
        { status: 400 }
      );
    }

    // Simple insert into database
    const supabase = createClient();
    const { data, error } = await supabase
      .from('simple_messages')
      .insert({
        text: text,
        role: 'user',
        ai_teammate: null
      })
      .select()
      .single();

    if (error) {
      console.error('Error inserting simple user message:', error);
      return NextResponse.json(
        { error: 'Failed to insert message' },
        { status: 500 }
      );
    }

    console.log(`✅ Simple user message inserted`);

    return NextResponse.json({
      success: true,
      message: 'User message inserted successfully',
      messageId: data.id
    });
  } catch (error) {
    console.error('Error in simple user route:', error);
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    );
  }
}
