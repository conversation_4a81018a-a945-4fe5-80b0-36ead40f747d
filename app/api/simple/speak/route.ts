import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { getUser } from '@/utils/supabase/queries';
import { getUserFromRequest } from '@/lib/apiKeys';

// Simple route for MCP server to insert assistant messages
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { text, ai_teammate, user_id, external_user_id } = body;

    if (!text) {
      return NextResponse.json(
        { error: 'Text is required' },
        { status: 400 }
      );
    }

    const supabase = createClient();

    // Determine user_id for the message
    let targetUserId: string | null = null;

    // Priority: explicit user_id > API key user > logged-in user
    if (user_id) {
      targetUserId = user_id;
      console.log('🤖 Assistant message for explicit user:', targetUserId);
    } else {
      // Check for API key first (for external MCP calls)
      targetUserId = await getUserFromRequest(request);
      if (targetUserId) {
        console.log('🤖 Assistant message for API key user:', targetUserId);
      } else {
        // Fallback to logged-in user (for web app calls)
        try {
          const user = await getUser(supabase);
          if (user) {
            targetUserId = user.id;
            console.log('🤖 Assistant message for logged-in user:', targetUserId);
          }
        } catch (error) {
          console.log('🤖 No authentication found, creating anonymous assistant message');
        }
      }
    }

    // Insert assistant message with user context
    const { data, error } = await supabase
      .from('simple_messages')
      .insert({
        text: text,
        role: 'assistant',
        ai_teammate: ai_teammate || 'system',
        user_id: targetUserId, // Will be added by migration
        external_user_id: external_user_id || null // Will be added by migration
      })
      .select()
      .single();

    if (error) {
      console.error('Error inserting simple message:', error);
      return NextResponse.json(
        { error: 'Failed to insert message' },
        { status: 500 }
      );
    }

    console.log(`✅ Simple message inserted - AI teammate: ${ai_teammate || 'system'}`);

    return NextResponse.json({
      success: true,
      message: 'Message inserted successfully',
      messageId: data.id,
      ai_teammate: ai_teammate || 'system'
    });
  } catch (error) {
    console.error('Error in simple speak route:', error);
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    );
  }
}
