import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

// Simple route for MCP server to insert assistant messages
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { text, ai_teammate } = body;

    if (!text) {
      return NextResponse.json(
        { error: 'Text is required' },
        { status: 400 }
      );
    }

    // Simple insert into database
    const supabase = createClient();
    const { data, error } = await supabase
      .from('simple_messages')
      .insert({
        text: text,
        role: 'assistant',
        ai_teammate: ai_teammate || 'system'
      })
      .select()
      .single();

    if (error) {
      console.error('Error inserting simple message:', error);
      return NextResponse.json(
        { error: 'Failed to insert message' },
        { status: 500 }
      );
    }

    console.log(`✅ Simple message inserted - AI teammate: ${ai_teammate || 'system'}`);

    return NextResponse.json({
      success: true,
      message: 'Message inserted successfully',
      messageId: data.id,
      ai_teammate: ai_teammate || 'system'
    });
  } catch (error) {
    console.error('Error in simple speak route:', error);
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    );
  }
}
