import { NextRequest, NextResponse } from 'next/server';
import { Webhooks } from '@octokit/webhooks';
import { handleInstallationCreated } from './handlers/installation-created';
import { handleInstallationDeleted } from './handlers/installation-deleted';

// Create webhooks instance with secret
const webhooks = new Webhooks({
  secret: process.env.GITHUB_WEBHOOK_SECRET || 'development-secret'
});

// Register event handlers (declarative approach!)
webhooks.on('installation.created', async ({ payload }) => {
  await handleInstallationCreated(payload);
});

webhooks.on('installation.deleted', handleInstallationDeleted);

webhooks.on('ping', ({ payload }) => {
  console.log('🏓 Ping event received:', payload.zen);
});

// Handle other events with logging
webhooks.onAny(({ name }) => {
  if (!['installation', 'ping'].includes(name)) {
    console.log(`📋 ${name} event received - TODO: implement handler`);
  }
});

/**
 * GitHub Webhook Handler for Next.js App Router
 * Uses @octokit/webhooks for clean, declarative event handling
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.text();
    const signature = req.headers.get('x-hub-signature-256');
    const event = req.headers.get('x-github-event');
    const deliveryId = req.headers.get('x-github-delivery');

    console.log(`📨 GitHub webhook received: ${event} (${deliveryId})`);

    if (!signature || !event || !deliveryId) {
      console.error('❌ Missing required webhook headers');
      return NextResponse.json(
        { error: 'Missing required headers' },
        { status: 400 }
      );
    }

    // Let @octokit/webhooks handle verification and routing
    await webhooks.verifyAndReceive({
      id: deliveryId,
      name: event as any,
      signature,
      payload: body
    });

    console.log(`✅ Successfully processed ${event} webhook`);
    return NextResponse.json({ received: true });

  } catch (error) {
    console.error('❌ Webhook processing error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Health check endpoint
export async function GET() {
  return NextResponse.json({
    status: 'ok',
    service: 'github-webhook-handler',
    timestamp: new Date().toISOString(),
    endpoint: '/api/github/webhooks',
    approach: 'declarative-with-octokit-webhooks'
  });
}
