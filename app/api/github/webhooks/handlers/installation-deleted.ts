/**
 * GitHub App Installation Deleted Event Handler
 */
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

export async function handleInstallationDeleted(event: any) {
  const { installation } = event.payload;
  const account = installation.account;
  
  console.log('��️ Installation deleted', {
    account: account.login,
    type: account.type,
    installationId: installation.id
  });

  try {
    console.log('📊 Deleting installation from Supabase', {
      installationId: installation.id,
      accountLogin: account.login
    });

    const { error } = await supabase
      .from('github_installations')
      .delete()
      .eq('installation_id', installation.id);

    if (error) {
      throw new Error(`Supabase deletion failed: ${error.message}`);
    }

    console.log('✅ Installation deleted successfully', {
      installationId: installation.id,
      account: account.login
    });
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    
    console.error('❌ Failed to process installation deletion', {
      account: account.login,
      installationId: installation.id,
      error: errorMessage
    });
    throw error;
  }
}
