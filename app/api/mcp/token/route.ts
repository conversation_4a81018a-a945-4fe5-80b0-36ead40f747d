import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { getUser } from '@/utils/supabase/queries';
import { createAPIKey, getUserAPIKeys } from '@/lib/apiKeys';

// Generate simple API key for authenticated user
export async function POST(request: Request) {
  try {
    const supabase = createClient();
    const user = await getUser(supabase);

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Generate simple API key
    const { apiKey } = await createAPIKey(user.id);

    return NextResponse.json({
      success: true,
      token: apiKey,
      expiresIn: 'never',
      userId: user.id
    });

  } catch (error) {
    console.error('Error generating API key:', error);
    return NextResponse.json(
      { error: 'Failed to generate API key' },
      { status: 500 }
    );
  }
}

// Get user's existing API keys
export async function GET(request: Request) {
  try {
    const supabase = createClient();
    const user = await getUser(supabase);

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user's API keys (without exposing the actual keys)
    const apiKeys = await getUserAPIKeys(user.id);

    const safeKeys = apiKeys.map(key => ({
      id: key.id,
      createdAt: key.created_at,
      active: key.active
    }));

    return NextResponse.json({
      success: true,
      userId: user.id,
      email: user.email,
      apiKeys: safeKeys,
      message: 'Use POST to generate a new API key'
    });

  } catch (error) {
    console.error('Error getting API keys:', error);
    return NextResponse.json(
      { error: 'Failed to get API keys' },
      { status: 500 }
    );
  }
}
