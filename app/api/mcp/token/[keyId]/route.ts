import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { getUser } from '@/utils/supabase/queries';
import { revokeAPIKey } from '@/lib/apiKeys';

// Revoke specific API key
export async function DELETE(
  request: Request,
  { params }: { params: { keyId: string } }
) {
  try {
    const supabase = createClient();
    const user = await getUser(supabase);

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const keyId = params.keyId;
    
    if (!keyId) {
      return NextResponse.json(
        { error: 'Key ID is required' },
        { status: 400 }
      );
    }

    // Revoke the API key
    await revokeAPIKey(keyId, user.id);

    return NextResponse.json({
      success: true,
      message: 'API key revoked successfully',
      keyId: keyId
    });

  } catch (error) {
    console.error('Error revoking API key:', error);
    return NextResponse.json(
      { error: 'Failed to revoke API key' },
      { status: 500 }
    );
  }
}
