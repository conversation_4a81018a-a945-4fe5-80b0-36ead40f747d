import { NextResponse } from 'next/server';
import { getUnsentMessages, markMessagesAsSent, getUnsentMessagesSync, markMessagesAsSentSync } from '../../../../lib/messageStore';
import { getUserFromRequest } from '../../../../lib/apiKeys';
import { createClient } from '../../../../utils/supabase/server';

// Route for MCP tool to get unsent user messages
export async function GET(request: Request) {
  try {
    // Log authentication headers for debugging
    const authHeader = request.headers.get('Authorization');
    console.log(`🔐 User Messages - Auth Header: ${authHeader ? `${authHeader.substring(0, 20)}...` : 'None'}`);

    // Extract user from authentication (API key or JWT)
    const userId = await getUserFromRequest(request);
    console.log(`👤 User Messages - User ID: ${userId || 'Anonymous'}`);

    // Get the URL parameters
    const { searchParams } = new URL(request.url);
    const getAll = searchParams.get('all') === 'true';

    let messages: any[] = [];

    // Get messages from database if authenticated (simple!)
    if (userId) {
      const supabase = createClient();
      let query = supabase
        .from('voice_messages')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: true });

      if (!getAll) {
        query = query.eq('sent', false).eq('role', 'user');
      }

      const { data, error } = await query;

      if (!error && data) {
        // Convert to legacy format
        messages = data.map(msg => ({
          id: msg.id,
          text: msg.text,
          timestamp: msg.created_at,
          role: msg.role,
          sent: msg.sent,
          spoken: msg.spoken,
          ai_teammate: msg.ai_teammate
        }));

        // Mark as sent if needed
        if (!getAll && messages.length > 0) {
          await supabase
            .from('voice_messages')
            .update({ sent: true })
            .in('id', messages.map(m => m.id));
        }

        console.log(`✅ Database messages retrieved - Count: ${messages.length}`);
      }
    }

    // Fallback to memory if no database messages
    if (messages.length === 0) {
      if (getAll) {
        messages = getUnsentMessagesSync('user', true).concat(getUnsentMessagesSync('assistant', true));
        messages.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
      } else {
        messages = getUnsentMessagesSync('user', false);
      }

      if (messages.length > 0) {
        markMessagesAsSentSync(messages);
      }
      console.log(`✅ Memory messages retrieved - Count: ${messages.length}`);
    }

    return NextResponse.json({
      messages,
      authenticated: !!userId,
      userId: userId || null
    });

  } catch (error) {
    console.error('Error in GET /api/mcp/user-messages:', error);
    return NextResponse.json(
      { error: 'Failed to get user messages' },
      { status: 500 }
    );
  }
}
