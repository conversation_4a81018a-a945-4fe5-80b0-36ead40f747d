import { NextResponse } from 'next/server';
import { addMessage, getUnsentMessages, markMessagesAsSent, getUnsentMessagesSync, markMessagesAsSentSync } from '@/lib/messageStore';
import { getUserFromRequest } from '@/lib/apiKeys';
import { createClient } from '@/utils/supabase/server';

// Route for MCP tool to send messages to be spoken
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { text, ai_teammate } = body;

    if (!text) {
      return NextResponse.json(
        { error: 'Text is required' },
        { status: 400 }
      );
    }

    // Get user ID from Bearer token (simple!)
    const authHeader = request.headers.get('Authorization');
    const userId = await getUserFromRequest(request);
    console.log(`🔐 MCP Speak - User: ${userId || 'anonymous'}`);

    // Insert into voice_messages table (simple!)
    if (userId) {
      const supabase = createClient();
      const { error } = await supabase
        .from('voice_messages')
        .insert({
          user_id: userId,
          session_id: null,
          text: text,
          role: 'assistant',
          ai_teammate: ai_teammate || 'system',
          spoken: false,
          sent: false
        });

      if (error) {
        console.error('Database error:', error);
      } else {
        console.log('✅ Message stored in database');
      }
    }

    // Also add to memory for backward compatibility
    const message = addMessage({
      text,
      role: 'assistant',
      sent: false,
      ai_teammate: ai_teammate || 'system'
    }, userId || undefined);

    return NextResponse.json({
      success: true,
      message: 'Message received and will be spoken',
      messageId: message.id,
      ai_teammate: ai_teammate || 'system',
      authenticated: !!userId,
      userId: userId || null
    });
  } catch (error) {
    console.error('Error processing MCP speak request:', error);
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    );
  }
}

// Route for web app to get unsent messages from MCP tool
export async function GET(request: Request) {
  try {
    // Get the URL parameters
    const { searchParams } = new URL(request.url);
    const getAll = searchParams.get('all') === 'true';
    const role  = (searchParams.get('role') || 'assistant') as 'assistant' | 'user';

    if (getAll) {
      // Return all messages if requested (for debugging)
      // Use async version for better data consistency
      try {
        const allMessages = await getUnsentMessages(role, true);
        return NextResponse.json({ messages: allMessages });
      } catch (error) {
        console.error('Error getting all messages, falling back to sync:', error);
        const allMessages = getUnsentMessagesSync(role, true);
        return NextResponse.json({ messages: allMessages });
      }
    }

    // Get unsent messages from MCP tool (role: 'assistant')
    // Use async version for better data consistency
    try {
      const unsentMessages = await getUnsentMessages(role);

      // Mark these messages as sent
      await markMessagesAsSent(unsentMessages);

      // Return only the unsent messages
      return NextResponse.json({ messages: unsentMessages });
    } catch (error) {
      console.error('Error with async message operations, falling back to sync:', error);
      const unsentMessages = getUnsentMessagesSync(role);
      markMessagesAsSentSync(unsentMessages);
      return NextResponse.json({ messages: unsentMessages });
    }

  } catch (error) {
    console.error('Error in GET /api/mcp/speak:', error);
    return NextResponse.json(
      { error: 'Failed to get messages' },
      { status: 500 }
    );
  }
}
