import { createClient } from '@/utils/supabase/server';
import { redirect } from 'next/navigation';
import GitHubDashboard from '@/components/github/GitHubDashboard';

export default async function GitHubPage() {
  const supabase = createClient();

  // Check if user is authenticated
  const {
    data: { user },
    error: userError
  } = await supabase.auth.getUser();

  if (userError || !user) {
    redirect('/signin');
  }

  // Fetch GitHub installations
  const { data: installations, error: installationsError } = await supabase
    .from('github_installations')
    .select('*')
    .order('created_at', { ascending: false });

  // Fetch recent GitHub events
  const { data: pushEvents, error: pushError } = await supabase
    .from('github_push_events')
    .select('*')
    .order('created_at', { ascending: false })
    .limit(10);

  const { data: prEvents, error: prError } = await supabase
    .from('github_pull_request_events')
    .select('*')
    .order('created_at', { ascending: false })
    .limit(10);

  const { data: issueEvents, error: issueError } = await supabase
    .from('github_issue_events')
    .select('*')
    .order('created_at', { ascending: false })
    .limit(10);

  return (
    <div className="min-h-screen bg-black text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">
            GitHub Integration Dashboard
          </h1>
          <p className="text-gray-400">
            Real-time GitHub webhook events and installation management
          </p>
        </div>

        <GitHubDashboard
          installations={installations || []}
          pushEvents={pushEvents || []}
          prEvents={prEvents || []}
          issueEvents={issueEvents || []}
          errors={{
            installations: installationsError,
            pushEvents: pushError,
            prEvents: prError,
            issueEvents: issueError
          }}
        />
      </div>
    </div>
  );
}
