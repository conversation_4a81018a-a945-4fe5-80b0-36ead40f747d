'use client';

import MessageList from '@/components/voice/MessageList';
import ThemeProvider from '@/components/voice/ThemeProvider';

export default function VoicePage() {
  return (
    <ThemeProvider>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <div className="mb-8 text-center">
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                AI Voice Interface
              </h1>
              <p className="text-gray-600 dark:text-gray-300">
                Communicate with AI teammates through voice and text
              </p>
            </div>
            
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 h-[calc(100vh-200px)]">
              <MessageList />
            </div>
          </div>
        </div>
      </div>
    </ThemeProvider>
  );
}
