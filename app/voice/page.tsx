'use client';

import MessageList from '@/components/voice/MessageList';
import ThemeProvider from '@/components/voice/ThemeProvider';

export default function VoicePage() {
  return (
    <ThemeProvider>
      <div className="h-full w-full flex flex-col overflow-hidden">
        {/* Header with Navigation */}
        <div className="flex-shrink-0 bg-gray-800 border-b border-gray-700 px-6 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <a
                href="/"
                className="text-gray-300 hover:text-white transition-colors"
                title="Back to Home"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
              </a>
              <div>
                <h1 className="text-xl font-bold text-white">
                  AI Voice Interface
                </h1>
                <p className="text-xs text-gray-300">
                  Communicate with AI teammates through voice and text
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <a
                href="/account"
                className="text-gray-300 hover:text-white transition-colors text-sm"
              >
                Account
              </a>
              <a
                href="/pricing"
                className="text-gray-300 hover:text-white transition-colors text-sm"
              >
                Pricing
              </a>
            </div>
          </div>
        </div>

        {/* Main Content - Full Height */}
        <div className="flex-1 overflow-hidden">
          <MessageList />
        </div>
      </div>
    </ThemeProvider>
  );
}
