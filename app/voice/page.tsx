'use client';

import MessageList from '@/components/voice/MessageList';
import ThemeProvider from '@/components/voice/ThemeProvider';

export default function VoicePage() {
  return (
    <ThemeProvider>
      <div className="h-screen flex flex-col bg-gray-50 dark:bg-gray-900">
        {/* Header */}
        <div className="flex-shrink-0 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
          <div className="max-w-7xl mx-auto">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              AI Voice Interface
            </h1>
            <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
              Communicate with AI teammates through voice and text
            </p>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 overflow-hidden">
          <div className="h-full max-w-7xl mx-auto px-6 py-4">
            <MessageList />
          </div>
        </div>
      </div>
    </ThemeProvider>
  );
}
