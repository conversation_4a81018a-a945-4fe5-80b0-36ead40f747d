#!/usr/bin/env node

/**
 * AI-SDLC Communication MCP Server
 * 
 * This MCP server integrates with the AI-SDLC voice interface using API keys.
 * It provides tools for:
 * - Getting new messages from users
 * - Speaking messages to users
 * - Managing voice conversations
 */

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
  Tool,
} from '@modelcontextprotocol/sdk/types.js';

// Configuration interface
interface MCPConfig {
  apiKey: string;
  baseUrl: string;
  userId?: string;
  externalUserId?: string;
}

// Message interface
interface Message {
  id: string;
  text: string;
  timestamp: string;
  role: 'user' | 'assistant';
  spoken?: boolean;
  ai_teammate?: string;
}

class CommunicationMCPServer {
  private server: Server;
  private config: MCPConfig;

  constructor() {
    this.server = new Server(
      {
        name: 'ai-sdlc-communication',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    // Get configuration from environment variables
    this.config = {
      apiKey: process.env.AISDLC_API_KEY || '',
      baseUrl: process.env.AISDLC_BASE_URL || 'http://localhost:3000',
      userId: process.env.AISDLC_USER_ID,
      externalUserId: process.env.AISDLC_EXTERNAL_USER_ID,
    };

    if (!this.config.apiKey) {
      console.error('❌ AISDLC_API_KEY environment variable is required');
      process.exit(1);
    }

    this.setupHandlers();
  }

  private setupHandlers() {
    // List available tools
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'get_user_messages',
            description: 'Get new messages from users in the voice interface',
            inputSchema: {
              type: 'object',
              properties: {
                all: {
                  type: 'boolean',
                  description: 'Get all messages instead of just unsent ones',
                  default: false,
                },
              },
            },
          },
          {
            name: 'speak_message',
            description: 'Send a message to be spoken in the voice interface',
            inputSchema: {
              type: 'object',
              properties: {
                text: {
                  type: 'string',
                  description: 'The message text to speak',
                },
                ai_teammate: {
                  type: 'string',
                  description: 'Which AI teammate is speaking (sarah, jordan, alex, system)',
                  default: 'system',
                },
                user_id: {
                  type: 'string',
                  description: 'Specific user ID to send message to (optional)',
                },
                external_user_id: {
                  type: 'string',
                  description: 'External user ID for tracking (optional)',
                },
              },
              required: ['text'],
            },
          },
          {
            name: 'send_user_message',
            description: 'Send a message as if it came from a user',
            inputSchema: {
              type: 'object',
              properties: {
                text: {
                  type: 'string',
                  description: 'The user message text',
                },
                external_user_id: {
                  type: 'string',
                  description: 'External user ID for tracking (optional)',
                },
              },
              required: ['text'],
            },
          },
          {
            name: 'get_conversation_history',
            description: 'Get recent conversation history from the voice interface',
            inputSchema: {
              type: 'object',
              properties: {
                limit: {
                  type: 'number',
                  description: 'Number of messages to retrieve',
                  default: 20,
                },
              },
            },
          },
        ] as Tool[],
      };
    });

    // Handle tool calls
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      try {
        switch (name) {
          case 'get_user_messages':
            return await this.getUserMessages(args?.all || false);

          case 'speak_message':
            return await this.speakMessage(
              args?.text,
              args?.ai_teammate || 'system',
              args?.user_id,
              args?.external_user_id
            );

          case 'send_user_message':
            return await this.sendUserMessage(
              args?.text,
              args?.external_user_id
            );

          case 'get_conversation_history':
            return await this.getConversationHistory(args?.limit || 20);

          default:
            throw new Error(`Unknown tool: ${name}`);
        }
      } catch (error) {
        return {
          content: [
            {
              type: 'text',
              text: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
            },
          ],
        };
      }
    });
  }

  private async makeRequest(endpoint: string, options: RequestInit = {}) {
    const url = `${this.config.baseUrl}${endpoint}`;
    const headers = {
      'Authorization': `Bearer ${this.config.apiKey}`,
      'Content-Type': 'application/json',
      ...options.headers,
    };

    console.log(`🌐 Making request to: ${url}`);
    
    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    return response.json();
  }

  private async getUserMessages(all: boolean = false) {
    try {
      // Use the new user-filtered endpoint
      const endpoint = all ? '/api/mcp/user-messages?all=true' : '/api/mcp/user-messages';
      const data = await this.makeRequest(endpoint);

      const messages = data.messages || [];
      const messageCount = messages.length;
      const authenticated = data.authenticated || false;
      const userId = data.userId || 'anonymous';

      return {
        content: [
          {
            type: 'text',
            text: `📨 Retrieved ${messageCount} ${all ? 'total' : 'new'} user messages\n` +
                  `🔐 Authenticated: ${authenticated ? '✅' : '❌'} (User: ${userId})\n\n` +
                  messages.map((msg: Message, index: number) =>
                    `${index + 1}. [${msg.role.toUpperCase()}] ${msg.text}\n` +
                    `   Time: ${new Date(msg.timestamp).toLocaleString()}\n` +
                    `   ID: ${msg.id}${msg.ai_teammate ? `\n   AI: ${msg.ai_teammate}` : ''}\n`
                  ).join('\n'),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Failed to get user messages: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async speakMessage(
    text: string,
    ai_teammate: string = 'system',
    user_id?: string,
    external_user_id?: string
  ) {
    if (!text) {
      throw new Error('Message text is required');
    }

    try {
      const payload = {
        text,
        ai_teammate,
        ...(user_id && { user_id }),
        ...(external_user_id && { external_user_id }),
      };

      const data = await this.makeRequest('/api/simple/speak', {
        method: 'POST',
        body: JSON.stringify(payload),
      });

      return {
        content: [
          {
            type: 'text',
            text: `🎤 Message sent successfully!\n\n` +
                  `AI Teammate: ${ai_teammate}\n` +
                  `Message: "${text}"\n` +
                  `Message ID: ${data.id || 'N/A'}\n` +
                  `User Context: ${user_id || 'API Key User'}\n` +
                  `External User: ${external_user_id || 'N/A'}\n` +
                  `Status: ✅ Delivered to voice interface`,
          },
        ],
      };
    } catch (error) {
      throw new Error(`Failed to speak message: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async sendUserMessage(text: string, external_user_id?: string) {
    if (!text) {
      throw new Error('Message text is required');
    }

    try {
      const payload = {
        text,
        ...(external_user_id && { external_user_id }),
      };

      const data = await this.makeRequest('/api/simple/user', {
        method: 'POST',
        body: JSON.stringify(payload),
      });

      return {
        content: [
          {
            type: 'text',
            text: `👤 User message sent successfully!\n\n` +
                  `Message: "${text}"\n` +
                  `Message ID: ${data.id || 'N/A'}\n` +
                  `External User: ${external_user_id || 'N/A'}\n` +
                  `Status: ✅ Delivered to voice interface`,
          },
        ],
      };
    } catch (error) {
      throw new Error(`Failed to send user message: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async getConversationHistory(limit: number = 20) {
    try {
      // Use the user-filtered endpoint for conversation history
      const data = await this.makeRequest(`/api/mcp/user-messages?all=true`);
      const messages = (data.messages || []).slice(-limit);
      const authenticated = data.authenticated || false;
      const userId = data.userId || 'anonymous';

      return {
        content: [
          {
            type: 'text',
            text: `📚 Conversation History (last ${messages.length} messages)\n` +
                  `🔐 Authenticated: ${authenticated ? '✅' : '❌'} (User: ${userId})\n\n` +
                  messages.map((msg: Message, index: number) =>
                    `${index + 1}. [${msg.role.toUpperCase()}] ${msg.text}\n` +
                    `   Time: ${new Date(msg.timestamp).toLocaleString()}\n` +
                    `   ${msg.ai_teammate ? `AI: ${msg.ai_teammate}` : 'User message'}\n`
                  ).join('\n'),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Failed to get conversation history: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('🚀 AI-SDLC Communication MCP Server running');
    console.error(`🔑 Using API key: ${this.config.apiKey.substring(0, 8)}...`);
    console.error(`🌐 Base URL: ${this.config.baseUrl}`);
  }
}

// Start the server
const server = new CommunicationMCPServer();
server.run().catch((error) => {
  console.error('❌ Failed to start MCP server:', error);
  process.exit(1);
});
