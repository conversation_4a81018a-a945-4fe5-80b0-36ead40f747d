{"name": "ai-sdlc-communication-mcp-server", "version": "1.0.0", "description": "MCP Server for AI-SDLC Communication with API Key support", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsc && node dist/index.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0"}, "keywords": ["mcp", "ai-sdlc", "communication", "voice"], "author": "AI-SDLC Team", "license": "MIT"}