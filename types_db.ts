
export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  graphql_public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      graphql: {
        Args: {
          operationName?: string
          query?: string
          variables?: Json
          extensions?: Json
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      api_keys: {
        Row: {
          active: boolean | null
          api_key: string
          created_at: string | null
          id: string
          user_id: string | null
        }
        Insert: {
          active?: boolean | null
          api_key: string
          created_at?: string | null
          id?: string
          user_id?: string | null
        }
        Update: {
          active?: boolean | null
          api_key?: string
          created_at?: string | null
          id?: string
          user_id?: string | null
        }
        Relationships: []
      }
      customers: {
        Row: {
          id: string
          stripe_customer_id: string | null
        }
        Insert: {
          id: string
          stripe_customer_id?: string | null
        }
        Update: {
          id?: string
          stripe_customer_id?: string | null
        }
        Relationships: []
      }
      github_installations: {
        Row: {
          access_token: string | null
          account_id: number
          account_login: string
          account_type: string
          created_at: string
          expires_at: string | null
          id: number
          inserted_at: string
          installation_id: number
          permissions: Json
          repository_selection: string
          updated_at: string
        }
        Insert: {
          access_token?: string | null
          account_id: number
          account_login: string
          account_type: string
          created_at: string
          expires_at?: string | null
          id?: number
          inserted_at?: string
          installation_id: number
          permissions?: Json
          repository_selection: string
          updated_at: string
        }
        Update: {
          access_token?: string | null
          account_id?: number
          account_login?: string
          account_type?: string
          created_at?: string
          expires_at?: string | null
          id?: number
          inserted_at?: string
          installation_id?: number
          permissions?: Json
          repository_selection?: string
          updated_at?: string
        }
        Relationships: []
      }
      github_issue_events: {
        Row: {
          action: string
          created_at: string
          id: number
          inserted_at: string
          installation_id: number | null
          issue_id: number
          issue_number: number
          labels: string[]
          repository_id: number
          repository_name: string
          sender_login: string
          state: string
          title: string
          user_login: string
        }
        Insert: {
          action: string
          created_at: string
          id?: number
          inserted_at?: string
          installation_id?: number | null
          issue_id: number
          issue_number: number
          labels?: string[]
          repository_id: number
          repository_name: string
          sender_login: string
          state: string
          title: string
          user_login: string
        }
        Update: {
          action?: string
          created_at?: string
          id?: number
          inserted_at?: string
          installation_id?: number | null
          issue_id?: number
          issue_number?: number
          labels?: string[]
          repository_id?: number
          repository_name?: string
          sender_login?: string
          state?: string
          title?: string
          user_login?: string
        }
        Relationships: []
      }
      github_pull_request_events: {
        Row: {
          action: string
          base_ref: string
          created_at: string
          head_ref: string
          id: number
          inserted_at: string
          installation_id: number | null
          pull_request_id: number
          pull_request_number: number
          repository_id: number
          repository_name: string
          sender_login: string
          state: string
          title: string
          user_login: string
        }
        Insert: {
          action: string
          base_ref: string
          created_at: string
          head_ref: string
          id?: number
          inserted_at?: string
          installation_id?: number | null
          pull_request_id: number
          pull_request_number: number
          repository_id: number
          repository_name: string
          sender_login: string
          state: string
          title: string
          user_login: string
        }
        Update: {
          action?: string
          base_ref?: string
          created_at?: string
          head_ref?: string
          id?: number
          inserted_at?: string
          installation_id?: number | null
          pull_request_id?: number
          pull_request_number?: number
          repository_id?: number
          repository_name?: string
          sender_login?: string
          state?: string
          title?: string
          user_login?: string
        }
        Relationships: []
      }
      github_push_events: {
        Row: {
          after_sha: string
          before_sha: string
          commit_count: number
          created_at: string
          id: number
          inserted_at: string
          installation_id: number | null
          pusher_email: string
          pusher_name: string
          ref: string
          repository_id: number
          repository_name: string
          sender_login: string
        }
        Insert: {
          after_sha: string
          before_sha: string
          commit_count?: number
          created_at: string
          id?: number
          inserted_at?: string
          installation_id?: number | null
          pusher_email: string
          pusher_name: string
          ref: string
          repository_id: number
          repository_name: string
          sender_login: string
        }
        Update: {
          after_sha?: string
          before_sha?: string
          commit_count?: number
          created_at?: string
          id?: number
          inserted_at?: string
          installation_id?: number | null
          pusher_email?: string
          pusher_name?: string
          ref?: string
          repository_id?: number
          repository_name?: string
          sender_login?: string
        }
        Relationships: []
      }
      github_repositories: {
        Row: {
          clone_url: string
          created_at: string
          default_branch: string
          description: string | null
          full_name: string
          html_url: string
          id: number
          inserted_at: string
          installation_id: number | null
          name: string
          owner_login: string
          owner_type: string
          private: boolean
          pushed_at: string | null
          repository_id: number
          ssh_url: string
          updated_at: string
        }
        Insert: {
          clone_url: string
          created_at: string
          default_branch?: string
          description?: string | null
          full_name: string
          html_url: string
          id?: number
          inserted_at?: string
          installation_id?: number | null
          name: string
          owner_login: string
          owner_type: string
          private?: boolean
          pushed_at?: string | null
          repository_id: number
          ssh_url: string
          updated_at: string
        }
        Update: {
          clone_url?: string
          created_at?: string
          default_branch?: string
          description?: string | null
          full_name?: string
          html_url?: string
          id?: number
          inserted_at?: string
          installation_id?: number | null
          name?: string
          owner_login?: string
          owner_type?: string
          private?: boolean
          pushed_at?: string | null
          repository_id?: number
          ssh_url?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "github_repositories_installation_id_fkey"
            columns: ["installation_id"]
            isOneToOne: false
            referencedRelation: "github_installations"
            referencedColumns: ["installation_id"]
          },
        ]
      }
      prices: {
        Row: {
          active: boolean | null
          currency: string | null
          description: string | null
          id: string
          interval: Database["public"]["Enums"]["pricing_plan_interval"] | null
          interval_count: number | null
          metadata: Json | null
          product_id: string | null
          trial_period_days: number | null
          type: Database["public"]["Enums"]["pricing_type"] | null
          unit_amount: number | null
        }
        Insert: {
          active?: boolean | null
          currency?: string | null
          description?: string | null
          id: string
          interval?: Database["public"]["Enums"]["pricing_plan_interval"] | null
          interval_count?: number | null
          metadata?: Json | null
          product_id?: string | null
          trial_period_days?: number | null
          type?: Database["public"]["Enums"]["pricing_type"] | null
          unit_amount?: number | null
        }
        Update: {
          active?: boolean | null
          currency?: string | null
          description?: string | null
          id?: string
          interval?: Database["public"]["Enums"]["pricing_plan_interval"] | null
          interval_count?: number | null
          metadata?: Json | null
          product_id?: string | null
          trial_period_days?: number | null
          type?: Database["public"]["Enums"]["pricing_type"] | null
          unit_amount?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "prices_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      products: {
        Row: {
          active: boolean | null
          description: string | null
          id: string
          image: string | null
          metadata: Json | null
          name: string | null
        }
        Insert: {
          active?: boolean | null
          description?: string | null
          id: string
          image?: string | null
          metadata?: Json | null
          name?: string | null
        }
        Update: {
          active?: boolean | null
          description?: string | null
          id?: string
          image?: string | null
          metadata?: Json | null
          name?: string | null
        }
        Relationships: []
      }
      simple_messages: {
        Row: {
          ai_teammate: string | null
          created_at: string | null
          id: string
          role: string
          text: string
        }
        Insert: {
          ai_teammate?: string | null
          created_at?: string | null
          id?: string
          role: string
          text: string
        }
        Update: {
          ai_teammate?: string | null
          created_at?: string | null
          id?: string
          role?: string
          text?: string
        }
        Relationships: []
      }
      subscriptions: {
        Row: {
          cancel_at: string | null
          cancel_at_period_end: boolean | null
          canceled_at: string | null
          created: string
          current_period_end: string
          current_period_start: string
          ended_at: string | null
          id: string
          metadata: Json | null
          price_id: string | null
          quantity: number | null
          status: Database["public"]["Enums"]["subscription_status"] | null
          trial_end: string | null
          trial_start: string | null
          user_id: string
        }
        Insert: {
          cancel_at?: string | null
          cancel_at_period_end?: boolean | null
          canceled_at?: string | null
          created?: string
          current_period_end?: string
          current_period_start?: string
          ended_at?: string | null
          id: string
          metadata?: Json | null
          price_id?: string | null
          quantity?: number | null
          status?: Database["public"]["Enums"]["subscription_status"] | null
          trial_end?: string | null
          trial_start?: string | null
          user_id: string
        }
        Update: {
          cancel_at?: string | null
          cancel_at_period_end?: boolean | null
          canceled_at?: string | null
          created?: string
          current_period_end?: string
          current_period_start?: string
          ended_at?: string | null
          id?: string
          metadata?: Json | null
          price_id?: string | null
          quantity?: number | null
          status?: Database["public"]["Enums"]["subscription_status"] | null
          trial_end?: string | null
          trial_start?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "subscriptions_price_id_fkey"
            columns: ["price_id"]
            isOneToOne: false
            referencedRelation: "prices"
            referencedColumns: ["id"]
          },
        ]
      }
      users: {
        Row: {
          avatar_url: string | null
          billing_address: Json | null
          full_name: string | null
          id: string
          payment_method: Json | null
        }
        Insert: {
          avatar_url?: string | null
          billing_address?: Json | null
          full_name?: string | null
          id: string
          payment_method?: Json | null
        }
        Update: {
          avatar_url?: string | null
          billing_address?: Json | null
          full_name?: string | null
          id?: string
          payment_method?: Json | null
        }
        Relationships: []
      }
      voice_messages: {
        Row: {
          ai_teammate: string | null
          created_at: string | null
          id: string
          role: string
          sent: boolean | null
          session_id: string | null
          spoken: boolean | null
          text: string
          user_id: string | null
        }
        Insert: {
          ai_teammate?: string | null
          created_at?: string | null
          id?: string
          role: string
          sent?: boolean | null
          session_id?: string | null
          spoken?: boolean | null
          text: string
          user_id?: string | null
        }
        Update: {
          ai_teammate?: string | null
          created_at?: string | null
          id?: string
          role?: string
          sent?: boolean | null
          session_id?: string | null
          spoken?: boolean | null
          text?: string
          user_id?: string | null
        }
        Relationships: []
      }
      voice_sessions: {
        Row: {
          active: boolean | null
          created_at: string | null
          id: string
          last_activity: string | null
          user_id: string | null
        }
        Insert: {
          active?: boolean | null
          created_at?: string | null
          id?: string
          last_activity?: string | null
          user_id?: string | null
        }
        Update: {
          active?: boolean | null
          created_at?: string | null
          id?: string
          last_activity?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      cleanup_inactive_api_keys: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      cleanup_inactive_voice_sessions: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
    }
    Enums: {
      pricing_plan_interval: "day" | "week" | "month" | "year"
      pricing_type: "one_time" | "recurring"
      subscription_status:
        | "trialing"
        | "active"
        | "canceled"
        | "incomplete"
        | "incomplete_expired"
        | "past_due"
        | "unpaid"
        | "paused"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {
      pricing_plan_interval: ["day", "week", "month", "year"],
      pricing_type: ["one_time", "recurring"],
      subscription_status: [
        "trialing",
        "active",
        "canceled",
        "incomplete",
        "incomplete_expired",
        "past_due",
        "unpaid",
        "paused",
      ],
    },
  },
} as const
